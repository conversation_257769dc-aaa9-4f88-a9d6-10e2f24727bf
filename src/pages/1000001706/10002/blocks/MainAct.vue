<template>
  <div id="main">
    <div class="content-container">
      <div class="title-wrapper">{{ title }}</div>
      <div class="text-wrapper">&nbsp; &nbsp; &nbsp;{{ content }}</div>
      <img
        class="btn-wrapper"
        @click="handleSign"
        src="https://img10.360buyimg.com/imgzone/jfs/t1/232909/23/11091/17047/65dfeb9dF0b95927f/3d69e56eca169830.png"
        alt=""
      />
      <div class="tip-wrapper">完成打卡后再次进入会员中心即可更新签到状态哦</div>
    </div>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable */
/* 快捷打开项目命令  npm run serve src/pages/1000001706/10002 */
import { inject, nextTick, onMounted, ref, Ref, reactive } from 'vue';
import { showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import { delayToast } from '../common';
import constant from '@/utils/constant';
import { callShare, setHeaderShare } from '@/utils/platforms/share';

// const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
// callShare({
//   title: '',
//   content: '',
//   shareUrl: `${process.env.VUE_APP_HOST}1000014485/1782958126539337730/?shareId=${shareConfig.shareId}&xxx=111`,
//   afterShare: () => { },
// });

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
const pathParams: any = inject('pathParams');
console.log('基础信息', baseInfo);
console.log('页面路径', pathParams);

// 活动链接
const actUrl = `${process.env.VUE_APP_HOST}${pathParams?.shopId}/${pathParams?.activityMainId}/?adSource=${pathParams.adsource}`;

const content = ref('');
const title = ref('');
const signStatus = ref(false);

// 获取签到信息
const handleSignInfo = async () => {
  try {
    const res = await httpRequest.post('/zhipu/royal/calendar');
    signStatus.value = res.data.sign;
  } catch (error: any) {
    console.error(error);
  }
};
// 获取知识百科信息
const handleKnowledgeInfo = async () => {
  try {
    const res = await httpRequest.get('/zhipu/royal/getKnowledge');
    content.value = res.data.content;
    title.value = res.data.title;
  } catch (error: any) {
    console.error(error);
  }
};
// 签到
const handleSign = async () => {
  try {
    if (signStatus.value) {
      showToast('您今天已打卡');
      return;
    }
    await httpRequest.post('/zhipu/royal/sign');
    signStatus.value = true;
    delayToast('打卡成功');
  } catch (error: any) {
    console.error(error);
  }
};
// 加载主接口
onMounted(() => {
  handleSignInfo();
  handleKnowledgeInfo();
});
</script>

<style lang="scss" scoped>
::-webkit-scrollbar {
  display: none;
}

#main {
  width: 7.5rem;
  max-width: 7.5rem;
  // min-height: 100vh;
  background-color: #fff;

  .content-container {
    background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/94364/23/46168/45961/65dfeb9cF58f858e3/a58ab09059f1425b.png);
    background-size: contain;
    background-repeat: no-repeat;
    width: 7.5rem;
    height: 13.24rem;
    padding-top: 3rem;
    box-sizing: border-box;

    .title-wrapper {
      font-size: 0.37rem;
      line-height: 0.37rem;
      margin: 0 auto;
      text-align: center;
      color: #ec001a;
      font-weight: bold;
    }

    .text-wrapper {
      width: 6.45rem;
      height: 4.8rem;
      margin: 0.5rem auto 0;
      font-size: 0.3rem;
      color: #ec001a;
      text-align: justify;
      overflow-y: scroll;
    }

    .btn-wrapper {
      width: 4.65rem;
      height: 0.8rem;
      display: block;
      margin: 1.98rem auto 0;
    }

    .tip-wrapper {
      font-size: 0.2rem;
      text-align: center;
      color: #ec001a;
      line-height: 0.2rem;
      margin-top: 0.2rem;
    }
  }
}
</style>

<style>
.gray-status {
  filter: grayscale(1);
}
</style>
