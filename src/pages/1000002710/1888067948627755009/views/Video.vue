<script lang="ts" setup>
/* eslint-disable */
import { ref, Ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { RootState } from '../store/state';
import { getMobileModel } from '../common';

const store = useStore<RootState>();

const birthStatus = computed(() => store.state.birthStatus);
const birthMonthId = computed(() => store.state.birthMonthId);

type DefineEmits = {
  (e: 'toggle-component', componentName: string): void;
};
const emits = defineEmits<DefineEmits>();

const seconds: Ref<number> = ref(60);
const btnStatus = computed(() => seconds.value === 0);
const isShowPlayBtn: Ref<boolean> = ref(true);
const videoRef = ref<HTMLVideoElement | null>(null);
const isPlayed: Ref<boolean> = ref(false);

const handleVideo = () => {
  videoRef.value.play();

  const system = getMobileModel();
  if (system !== 'iPhone') {
    videoRef.value.requestFullscreen();
  }

  isShowPlayBtn.value = false;
  if (isPlayed.value) {
    return;
  }
  isPlayed.value = true;
  // 触发按钮倒计时
  const timer = setInterval(() => {
    if (seconds.value > 0) {
      seconds.value--;
    } else {
      clearInterval(timer);
    }
  }, 1000);
};

const handleGetPrize = async (): Promise<any> => {
  try {
    showLoadingToast({});
    const res = await httpRequest.post('/kabrita/1888067948627755009/receiveRights', {
      stage: birthStatus.value === 1 ? birthMonthId.value : -1,
      type: birthStatus.value,
    });
    const {
      skuId,
      skuName,
      status,
    } = res.data;
    if (status === 1) {
      store.commit('setGetSuccess', {
        skuId,
        skuName,
      });
      emits('toggle-component', 'GetSuccessStatus');
    } else {
      emits('toggle-component', 'FailStatus');
    }
  } catch (error: any) {
    showToast(error);
  } finally {
    closeToast();
  }
};

onMounted(() => {
  videoRef.value = document.querySelector('video');
  videoRef.value.addEventListener('webkitendfullscreen', () => {
    console.log('iOS: 视频已退出全屏');
    isShowPlayBtn.value = true;
    videoRef.value.pause();
  });
  document.addEventListener('fullscreenchange', () => {
    if (!document.fullscreenElement) {
      console.log('非 iOS: 视频已退出全屏');
      isShowPlayBtn.value = true;
      videoRef.value.pause();
      // videoRef.value.currentTime = 0;
    }
  });
});
</script>
<template>
  <div id="video">
    <div class="video-container">
      <video id="video-source" poster="https://img10.360buyimg.com/imgzone/jfs/t1/260029/8/19423/39558/67ad9bfdF65733686/731ef1ca6c74a33f.jpg" preload="metadata" src="https://bigitem.oss-cn-zhangjiakou.aliyuncs.com/kabrita/168689/1686892025021301.mp4"></video>
      <img v-if="isShowPlayBtn" @click="handleVideo" src="https://img10.360buyimg.com/imgzone/jfs/t1/254250/7/20332/9819/67ada56aF9e51c1c6/1cca8430865535ba.png" alt="">
    </div>
    <div class="btn-container">
      <div class="btn-status-1" v-if="!btnStatus">
        还剩{{ seconds }}S
      </div>
      <div class="btn-status-2" @click="handleGetPrize()" v-else>
        <img src="https://img10.360buyimg.com/imgzone/jfs/t1/265422/18/19303/35878/67ada31dFa25e5e81/18dba1ded10fc393.png" alt="">
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
#video {
  width: 7.5rem;
  height: 18rem;
  background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/253937/20/20630/44617/67aea235Fa56ce958/4f2abd3ba7f40890.jpg");
  background-size: contain;
  background-repeat: no-repeat;
  padding-top: 6.57rem;
  padding-left: .3rem;
  box-sizing: border-box;

  .video-container {
    width: 6.95rem;
    height: 4.65rem;
    display: flex;
    align-items: center;
    position: relative;

    video {
      width: 100%;
      height: auto;
    }

    img {
      width: 2.48rem;
      height: 2.48rem;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .btn-container {
    max-width: 5.38rem;
    margin: 1.55rem auto 0;
    display: flex;
    justify-content: center;

    .btn-status-1 {
      width: 3.86rem;
      height: .88rem;
      border-radius: 1000px;
      background-color: #a1a1a1;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: .42rem;
      color: #fff;
    }

    .btn-status-2 {
      width: 5.38rem;
      height: .95rem;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
