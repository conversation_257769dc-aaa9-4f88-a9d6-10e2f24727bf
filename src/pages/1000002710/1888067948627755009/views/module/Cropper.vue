<template>
  <div id="cropper">
    <van-popup :lock-scroll="false" :show="isShow" safe-area-inset-bottom safe-area-inset-top teleport="body">
      <cropper
        :auto-zoom="false"
        :src="imageSource"
        :stencil-props="{
          aspectRatio: aspectRatio,
          class: 'cropper-stencil',
          previewClass: 'cropper-stencil__preview',
          draggingClass: 'cropper-stencil--dragging',
          handlersClasses: {
            default: 'cropper-handler',
            eastNorth: 'cropper-handler--east-north',
            westNorth: 'cropper-handler--west-north',
            eastSouth: 'cropper-handler--east-south',
            westSouth: 'cropper-handler--west-south',
          },
          handlersWrappersClasses: {
            default: 'cropper-handler-wrapper',
            eastNorth: 'cropper-handler-wrapper--east-north',
            westNorth: 'cropper-handler-wrapper--west-north',
            eastSouth: 'cropper-handler-wrapper--east-south',
            westSouth: 'cropper-handler-wrapper--west-south',
          },
        }"
        class="cropper"
        image-class="cropper__image"
        @change="handleCropperChange"
        @ready="handleCropperReady" />
      <div class="upload-btn" v-if="isShowBtn" @click="handleSuccess()">
        <img src="https://img10.360buyimg.com/imgzone/jfs/t1/259508/11/18983/9408/67ac4725F87956d5f/b6eaabc1cddafbf9.png" alt="">
      </div>
      <div class="cancel-btn" v-if="isShowBtn" @click="handleClose()">
        <img src="https://img10.360buyimg.com/imgzone/jfs/t1/232007/4/32137/9617/67ac4725F3e49d463/bd8c067effe87552.png" alt="">
      </div>
    </van-popup>
  </div>
</template>

<script lang="ts" setup>
/* eslint-disable */
import { ref, toRefs, Ref } from 'vue';
import { Cropper } from 'vue-advanced-cropper';
import 'vue-advanced-cropper/dist/style.css';

interface Props {
  imageSource: string;
  isShow: boolean;
  aspectRatio: number;
}

const props = withDefaults(defineProps<Props>(), {
  imageSource: 'https://img10.360buyimg.com/imgzone/jfs/t1/160464/30/47276/23712/66de97c2F3fc49a94/9622487457ec9f29.png',
  isShow: false,
  aspectRatio: 1,
});
type DefineEmits = {
  (e: 'close-popup', type: string): void;
  (e: 'image-data', src: string): void;
};
const emits = defineEmits<DefineEmits>();

const {
  imageSource,
  isShow,
  aspectRatio,
} = toRefs(props);

const handleClose = () => {
  emits('close-popup', 'cropper');
};

const isShowBtn: Ref<boolean> = ref(false);
/*
* 裁剪
* */
const fileBlob = ref<Blob | undefined>(undefined);
const handleCropperChange = (data: any): void => {
  const { canvas } = data;
  handleCompressor(canvas);
};
// 质量大小转换
const prettySize = (size: number) => {
  const kilobyte = 1024;
  let megabyte = kilobyte * kilobyte;

  if (size > megabyte) {
    return (size / megabyte).toFixed(2) + 'MB';
  } else if (size > kilobyte) {
    return (size / kilobyte).toFixed(2) + 'KB';
  } else if (size >= 0) {
    return size + 'B';
  }
  return 'N/A';
};

const compressorQuality: Ref<number> = ref(1);
const afterCompressorImageSrc: Ref<string> = ref('');
// 处理裁剪后的压缩
const handleCompressor = (canvas: any) => {
  canvas.toBlob(
    (blob: Blob) => {
      console.log('裁剪前', `${prettySize(blob.size)}，type：${blob.type}`);
      // 使用FileReader来读取Blob
      const reader = new FileReader();
      reader.onload = (e) => {
        if (blob.size >= 1024 * 1024) {
          console.log('裁剪成功', `${prettySize(blob.size)}，type：${blob.type}`);
          compressorQuality.value = Number((compressorQuality.value - 0.1).toFixed(1));
          handleCompressor(canvas);
        } else {
          afterCompressorImageSrc.value = e.target.result;
          console.log(compressorQuality.value, '此时的质量');
          fileBlob.value = blob;
          compressorQuality.value = 1;
        }
      };
      // 读取Blob为DataURL，同时指定图片格式和质量
      reader.readAsDataURL(blob);
    },
    'image/jpeg',
    compressorQuality.value,
  );
};
const handleSuccess = () => {
  // 操作太快会导致裁剪插件不完整
  // 等待图片处理完
  setTimeout(() => {
    emits('image-data', afterCompressorImageSrc.value);
    handleClose();
  }, 200);
};

const BUTTON_SHOW_DELAY = 700;
const handleCropperReady = (e) => {
  console.log('=>(Cropper.vue:135) e', e);
  setTimeout(() => {
    isShowBtn.value = true;
  }, BUTTON_SHOW_DELAY);
};
</script>

<style lang="scss">
.cropper-stencil {
  &__preview {
    &:after,
    &:before {
      content: '';
      opacity: 0;
      transition: opacity 0.25s;
      position: absolute;
      pointer-events: none;
      z-index: 1;
    }

    &:after {
      border-left: solid 1px white;
      border-right: solid 1px white;
      width: 33%;
      height: 100%;
      transform: translateX(-50%);
      left: 50%;
      top: 0;
    }

    &:before {
      border-top: solid 1px white;
      border-bottom: solid 1px white;
      height: 33%;
      width: 100%;
      transform: translateY(-50%);
      top: 50%;
      left: 0;
    }
  }

  &--dragging {
    .cropper-stencil__preview {
      &:after,
      &:before {
        opacity: 0.7;
      }
    }
  }
}

.cropper-line {
  border-color: rgba(white, 0.8);
}

.cropper-handler-wrapper {
  width: 24px;
  height: 24px;

  &--west-north {
    transform: translate(0, 0);
  }

  &--east-south {
    transform: translate(-100%, -100%);
  }

  &--west-south {
    transform: translate(0, -100%);
  }

  &--east-north {
    transform: translate(-100%, 0);
  }
}

.cropper-handler {
  display: block;
  position: relative;
  flex-shrink: 0;
  transition: opacity 0.5s;
  border: none;
  background: white;
  height: 4px;
  width: 4px;
  opacity: 0;
  top: auto;
  left: auto;

  &--west-north,
  &--east-south,
  &--west-south,
  &--east-north {
    display: block;
    height: 16px;
    width: 16px;
    background: none;
    opacity: 1;
  }

  &--west-north {
    border-left: solid 3px #ffe4b1;
    border-top: solid 3px #ffe4b1;
  }

  &--east-south {
    border-right: solid 3px #ffe4b1;
    border-bottom: solid 3px #ffe4b1;
  }

  &--west-south {
    border-left: solid 3px #ffe4b1;
    border-bottom: solid 3px #ffe4b1;
  }

  &--east-north {
    border-right: solid 3px #ffe4b1;
    border-top: solid 3px #ffe4b1;
  }

  &--hover {
    opacity: 1;
  }
}
</style>
<style lang="scss" scoped>
.cropper {
  width: 6.5rem;
  height: 8rem;
  // background: #ddd;
  position: relative;
  z-index: 3000;
}

.upload-btn {
  position: absolute;
  right: 0.3rem;
  bottom: 0;
  z-index: 3001;
  width: 1.18rem;
  height: 1.18rem;

  img {
    width: 100%;
    height: 100%;
  }
}

.cancel-btn {
  position: absolute;
  left: 0.3rem;
  bottom: 0;
  z-index: 3001;
  width: 1.18rem;
  height: 1.18rem;

  img {
    width: 100%;
    height: 100%;
  }
}
</style>
