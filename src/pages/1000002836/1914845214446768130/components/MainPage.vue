<template>
  <div class="main">
    <img class="main-btn" @click="handleOpenCard" src="../assets/main-btn.png" alt="" srcset="" />

    <div style="display: flex; justify-content: center">
      <van-checkbox v-model="agreeToTheAgreement">
        <div class="protocol protocol-info">点击上述按钮即代表同意宝洁中国处理我的个人订单信息</div>
      </van-checkbox>
    </div>
    <div class="protocol rule" @click="goLink(ruleLink)">活动详情</div>
    <img class="main-til" src="../assets/main-til.png" alt="" srcset="" />
    <div class="sku-list" v-if="list.length">
      <div class="sku-list-item" v-for="item in list" :key="item.id">
        <img :src="item.pic" alt="" srcset="" @click="goLink(item.link)" />
      </div>
    </div>
    <img class="main-shop" @click="gotoShopPage(baseInfo.shopId)" src="../assets/shop.png" alt="" />
  </div>
</template>

<script lang="ts" setup>
import type { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage } from '@/utils/platforms/jump';
import { ref, reactive, inject, computed } from 'vue';
import openCard from '@/utils/openCard';
import { orderTraceability } from '../scripts/ajax';
import { Checkbox, showToast } from 'vant';

const baseInfo = inject('baseInfo') as BaseInfo;
const decoData = inject('decoData') as any;
const agreeToTheAgreement = ref(false);
const list = computed(() => decoData?.mainList || []);
const goLink = (link: string) => {
  if (link) {
    window.location.href = link;
  }
};
const ruleLink = computed(() => decoData?.ruleLink || '');
const emits = defineEmits(['reissuePoints', 'noNeedToReIssue']);
const openCardCallback = async (res: any) => {
  console.log('openCardCallback', res);
  const result = await orderTraceability();
  if (result.isReplacement) {
    emits('reissuePoints', result.points);
  } else {
    emits('noNeedToReIssue', 0);
  }
};
const handleOpenCard = () => {
  if (!agreeToTheAgreement.value) {
    showToast('请先勾选授权按钮');
    return;
  }
  openCard(`${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`, {}, openCardCallback);
};
</script>

<style lang="scss" scoped>
.main {
  background: url('../assets/main.jpg') no-repeat;
  background-size: 100%;
  width: 10.8rem;
  height: 37.51rem;
  overflow: hidden;
  .main-btn {
    width: 7.17rem;
    margin: 4.41rem auto 0;
  }
  .protocol {
    font-size: 0.3rem;
    color: #fff;
    text-align: center;
    line-height: 0.5rem;
    margin: 0.1rem 1.2rem 0;
    &.protocol-info {
      margin: 0;
    }
    &.rule {
      font-size: 0.5rem;
      text-decoration: underline;
      background: linear-gradient(to bottom, #e9e1c6, #b18a5f);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-weight: bold;
    }
  }
  .main-til {
    width: 6.48rem;
    margin: 10.6rem auto 0;
  }
  .sku-list {
    margin-top: 0.85rem;
    max-height: 13.5rem;
    overflow-y: auto;
    .sku-list-item {
      width: 9.8rem;
      margin: 0.6rem auto 0;
      img {
        width: 100%;
      }
    }
  }
  .main-shop {
    width: 9.13rem;
    margin: 1rem auto 0;
  }
}
</style>
