<template>
  <Popup teleport="body" v-model:show="isShowPopup" :close-on-click-overlay="false" @closed="handleClose" @opened="handleOpen">
    <div class="box">
      <div class="title">竞猜结果</div>
      <div class="list">
        <List v-model:loading="loading" :finished="finished" finished-text="" loading-text="" @load="onLoad">
          <template v-if="guessList.length">
            <div class="recode-item" v-for="item in guessList" :key="item.headIcon">
              <img :src="item.headIcon" class="slide-icon" />
              用户{{ item.nickName }} 竞猜结果 {{ item.guessString }} {{ dayjs(item.guessTime).format('M月DD日 HH:mm') }}
            </div>
          </template>
          <div v-else-if="!loading && !guessList.length" class="no-data">暂未查询到竞猜信息~</div>
        </List>
      </div>

      <div class="close-icon" @click="handleClose"></div>
    </div>
  </Popup>
</template>
<script setup lang="ts">
import { computed, defineEmits, defineProps, inject, ref } from 'vue';
import { Popup, List } from 'vant';
import { getGuessList } from '../script/ajax';
import type { GuessItem } from '../script/type';
import dayjs from 'dayjs';

const props = defineProps({
  showPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});
const isShowPopup = computed(() => props.showPopup);
const emits = defineEmits(['closeDialog']);
const guessList = ref<GuessItem[]>([]);
const finished = ref(false);

const pageNum = ref(0);
const pageSize = ref(10);
const loading = ref(false);
const handleClose = () => {
  emits('closeDialog');
  pageNum.value = 0;
  finished.value = true;
  guessList.value = [];
};
const onLoad = async () => {
  try {
    pageNum.value++;
    const records = await getGuessList(pageNum.value, pageSize.value);
    loading.value = false;
    guessList.value = [...guessList.value, ...records];
    if (records.length < pageSize.value) {
      finished.value = true;
    }
  } catch (error: any) {
    console.log('🚀 ~ error:', error);
    loading.value = false;
    finished.value = true;
  }
};
const handleOpen = async () => {
  finished.value = false;
};
</script>
<style scoped lang="scss">
.box {
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/262914/1/13947/13767/678de2c7F56987419/38b9586f3267ba10.png) no-repeat;
  background-size: contain;
  width: 5.51rem;
  height: 8.23rem;
  padding: 0.23rem 0.2rem;
  box-sizing: border-box;
  position: relative;
  .title {
    font-size: 0.52rem;
    font-family: 'YouSheBiaoTiHei';
    text-align: center;
    color: #fff;
  }
  .list {
    margin-top: 0.2rem;
    max-height: 5.6rem;
    overflow-y: auto;
    .recode-item {
      background-color: #e4554d;
      font-family: OPPOSansR;
      width: 100%;
      padding: 0.1rem 0;
      box-sizing: border-box;
      line-height: 1;
      color: #fff;
      display: inline-block;
      border-radius: 0.25rem;
      font-size: 0.2rem;
      margin-bottom: 0.1rem;
      .slide-icon {
        display: inline-block;
        margin-right: 0.1rem;
        margin-left: 0.2rem;
        vertical-align: middle;
        width: 0.34rem;
        height: 0.34rem;
        background-color: #ffffff;
        border-radius: 50%;
      }
    }
  }
  .close-icon {
    position: absolute;
    width: 1rem;
    height: 1rem;
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);
  }
}
</style>
