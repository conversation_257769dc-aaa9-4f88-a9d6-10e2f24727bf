<template>
  <div class="main" :style="{ backgroundImage: `url(${decoData.bg})` }">
    <div class="content" :style="{ color: decoData.color }">
      <div class="title">恭喜您！领取成功</div>
      <div class="subtitle">{{ prizeData.prizeName }}</div>
      <img class="prize-img" :src="prizeData.prizeImg" alt="" />
      <div class="btn">
        <img v-if="prizeData.prizeType === 1" :src="decoData.toShopBtn" alt="" @click="gotoShopPage('1000074146')" />
        <img v-else-if="prizeData.prizeType === 7" :src="decoData.toCopyBtn" alt="" @click="showPassword" />
        <img v-else :src="decoData.confirmBtn" alt="" @click="hidePopup('award')" />
      </div>
      <div class="tip">{{ tips[prizeData.prizeType] ?? '' }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { preview } from '../../Utils';
import { hidePopup, popupData, showPopup } from '../DataHooks';
import { gotoShopPage } from '@/utils/platforms/jump';

const props = defineProps(['decoData']);
const prizeData = computed(() => popupData.award);

const tips: any = {
  6: '红包查看请前往“我的-我的钱包-红包”中查看详情',
  1: `您的优惠券已自动发放到您的账户
如需查看可点击“我的卡券”查看详情`,
  // 3: '地址需在24小时之内填写，如超24小时没填写需联系客服',
};

const showPassword = () => {
  showPopup('copyPassword', { num: prizeData.value.cardNumber, password: prizeData.value.cardPassword, tipImg: '' });
  hidePopup('award');
};
</script>

<style scoped lang="scss">
.main {
  width: 6.23rem;
  height: 7.82rem;
  background-size: 100%;
  background-repeat: no-repeat;
  padding: 0.83rem 0.02rem 0.02rem;
  .content {
    .title {
      font-size: 0.5rem;
      text-align: center;
      margin-bottom: 0.15rem;
    }
    .subtitle {
      font-size: 0.3rem;
      text-align: center;
    }
    .prize-img {
      width: 5.5rem;
      height: 2.75rem;
      object-fit: contain;
      margin: 0.3rem auto;
    }
    .btn {
      padding-top: 0.65rem;
      padding-bottom: 0.46rem;
      img {
        width: 3.6rem;
        margin: 0 auto;
      }
    }
    .tip {
      font-size: 0.18rem;
      text-align: center;
    }
  }
}
</style>
