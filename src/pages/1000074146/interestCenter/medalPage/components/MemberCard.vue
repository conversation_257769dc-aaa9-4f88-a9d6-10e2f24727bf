<template>
  <div>
    <img :src="decoData.bg" alt="会员卡" class="w-full" />
  </div>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';

const props = defineProps(['decorationInfo']);
const decoData = computed(() => props.decorationInfo);
</script>
<style scoped lang="scss">
.w-full {
  width: 7.5rem;
  //height: 4.28rem;
  display: block;
  margin: 0 auto -0.6rem;
  object-fit: contain;
  object-position: center;
}
</style>
