<template>
    <div :class="`phto-${photoStyle}`">
      <div :class="`cover-view-${photoStyle}`" @click="handleOpenPhotoPage(photoInfo)">
          <div class="cover-img" :style="{backgroundImage: `url(${photoInfo.workImageUrl})`}"></div>
      </div>
      <div class="photo-name" @click="handleOpenPhotoPage(photoInfo)">{{ photoInfo.workTitle }}</div>
      <div class="user-info">
        <div class="nick-img" :style="{backgroundImage: `url(${photoInfo.headerImg})`}"></div>
        <div class="nick-name">{{ photoInfo.nickName }}</div>
        <div class="praise-view">
          <img style="width: 0.25rem; height: 0.22rem;margin-right: 0.05rem;"
            :src="photoInfo.dayUpStatus?'https://img10.360buyimg.com/imgzone/jfs/t1/117198/28/35357/550/64585a46Fa54bb595/32adc95ad9a05a45.png':'//img10.360buyimg.com/imgzone/jfs/t1/204452/17/32502/894/645b2259Fbfda44e4/297c1f55258c945a.png'"
            @click="task(photoInfo)"
            />
          <div class="praise-num">{{ photoInfo.popularity }}</div>
        </div>
      </div>
      <img class="rank-icon" v-if="rankIconList[photoInfo.rankLimit]" :src="rankIconList[photoInfo.rankLimit]">
  </div>
</template>

<script setup lang='ts'>
import {
  ref, Ref, toRefs, defineProps, defineExpose, defineEmits, inject } from 'vue';
import { Checkbox as VanCheckbox, Button as VanButton, Popup as VanPopup, Toast, showToast } from 'vant';
import { BaseInfo } from '@/types/BaseInfo';
import { httpRequest } from '@/utils/service';

const delayToast = async (e: string) => {
  setTimeout(() => {
    Toast(e);
  }, 1000);
};

const loveIconCom = ref();

// 作品审核状态对应的图标 0:待审核 1：审核通过 2：审核未通过
const EXAMINE = {
  0: 'https://img10.360buyimg.com/imgzone/jfs/t1/134434/18/36560/4467/645efaefFd138391b/370b0eba8251c53e.png',
  2: 'https://img10.360buyimg.com/imgzone/jfs/t1/129193/40/32088/4844/645efaefFfe0782b4/77c2ba04f6d7f14b.png',
};
const isShow = ref(false);
const props = defineProps({
  isDisLove: {
    type: Boolean,
    required: false,
    default: false,
  },
  photoInfo: {
    type: Object,
    required: true,
  },
  isCheckCancel: {
    type: Boolean,
    required: false,
    default: false,
  },
  isMyPush: {
    type: Boolean,
    required: false,
    default: false,
  },
  photoStyle: {
    type: Number,
    required: false,
    default: 0,
  },
});
const { photoInfo } = toRefs(props);
const { isCheckCancel } = toRefs(props);
const { isMyPush } = toRefs(props);
const { isDisLove } = toRefs(props);

const rankIconList = {
  1: '//img10.360buyimg.com/imgzone/jfs/t1/266130/31/15127/6080/679309beF5c95e095/74fde280d7a9135e.png',
  2: '//img10.360buyimg.com/imgzone/jfs/t1/268446/24/15228/7412/679309beF645a6054/1a07c8636d3b615f.png',
  3: '//img10.360buyimg.com/imgzone/jfs/t1/262217/1/15086/6638/679309beF291bb572/0206ad5fc88ddb90.png',
  4: '//img10.360buyimg.com/imgzone/jfs/t1/263679/12/15023/6664/679309bdF5989b5a8/9debb0abc79524df.png',
  5: '//img10.360buyimg.com/imgzone/jfs/t1/263358/31/14604/6543/679309bdFb783f6b9/0908311bd02c2953.png',
  6: '//img10.360buyimg.com/imgzone/jfs/t1/255346/11/13832/6654/679309bdF487e1a2f/6a4dad2112cf3f7d.png',
  7: '//img10.360buyimg.com/imgzone/jfs/t1/264108/34/15094/6390/679309bdFa2ff1284/889d2cf897859a31.png',
  8: '//img10.360buyimg.com/imgzone/jfs/t1/266234/1/14982/6768/679309bcFd6c90e07/7d51c25f9c486265.png',
  9: '//img10.360buyimg.com/imgzone/jfs/t1/235754/5/35377/6640/679309bcFf782e92c/3efc7fb15c961b00.png',
  10: '//img10.360buyimg.com/imgzone/jfs/t1/258423/12/15283/6657/679309bcFb5f3f9b6/1166a95e42d20c00.png',
};

// const postInfo = inject('postInfo') as string;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
// let lock = false;
const emits = defineEmits(['handleOpenPhotoPage', 'handleCancelLove', 'handleTaskLove']);
const handleOpenPhotoPage = (photoId:string) => {
  // setTimeout(() => {
  console.log('photoId', photoId);
  emits('handleOpenPhotoPage', photoId);
  // }, 500);
};

const task = async (photoInfo: any) => {
  if (isDisLove.value) {
    console.log(isDisLove.value);
    return;
  }
  try {
    const config = {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    };
    const { data, message } = await httpRequest.post('/dz/1874673979168907266/upWorks', { workId: photoInfo.id }, {}, { isLoading: true });
    if (message) {
      showToast(message);
    } else {
      photoInfo.dayUpStatus = true;
      photoInfo.popularity += 100;
      emits('handleTaskLove', photoInfo.id);
    }
  } catch (error: any) {
    console.log(error);
    showToast(error.message);
  }
};

const isShowTopTips = ref(props);
// 显示暴露的数据，才可以在父组件拿到
defineExpose({
  isShow,
});

</script>

<style lang='scss' scoped>
.photo-view {
    width: 2.92rem;
    height: 4.54rem;
}
.cover-img {
  width: 100%;
  height: 100%;
  border-radius: 0.20rem;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/196595/16/34278/83680/645ddb4aFfbed970c/9d2f9e955a7ba666.png);
}
.cover-img-status {
  width: 2.88rem;
  height: 3.39rem;
  position: absolute;
  left: 0;
  top: 0;
}
.photo-name {
  margin-top: 0.2rem;
  width: 3.2rem;
  margin-left: 0.05rem;
  font-size: 0.26rem;
  color: #000;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.user-info {
  width: 3.2rem;
  margin-left: 0.05rem;
  height: 0.5rem;
  margin-top: 0.1rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.nick-img {
  width: 0.5rem;
  height: 0.5rem;
  margin-right: 0.05rem;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
.nick-name {
  width: 1rem;
  font-size: 0.17rem;
  color: #000;
}
.praise-view {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}
.praise-num {
  width: auto;
  white-space: nowrap;
  font-size: 0.18rem;
  color: #000;
}
  .content {
    width: 6.5rem;
    height: 8.84rem;
    background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/203525/37/22531/15665/6438fdd7Fc67238b4/78fdba30e081be33.png");
    background-size: 100%;
    background-repeat: no-repeat;
    padding-top: 1.46rem;
    box-sizing: border-box;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;

    .rule_text {
      width: 5.3rem;
      height: 5.9rem;
      color: #000;
      overflow-y: scroll;
      font-size: 0.18rem;
      text-align: justify;
    }

    .close-btn {
      width: .6rem;
      height: .6rem;
      margin-top: .84rem;
    }
  }
  .cover-view-0 {
    width: 3.4rem;
    height: 2.6rem;
  }
  .phto-0 {
    width: 3.4rem;
    height: 3.8rem;
    border-radius: 0.2rem;
    background-color: #fff;
    margin: 0.1rem;
    position: relative;
}
.cover-view-1 {
    width: 3.4rem;
    height: 3.92rem;
  }
.phto-1 {
    width: 3.4rem;
    height: 5.12rem;
    border-radius: 0.2rem;
    background-color: #fff;
    margin: 0.1rem;
    position: relative;
}
.rank-icon {
    width: 0.73rem;
    height: 0.46rem;
    position: absolute;
    left: 0.1rem;
    top: 0.1rem;
}
  </style>
  <style lang="scss">
  .rule_popup.van-popup {
    // width: 6.5rem;
    // height: 8.84rem;
    background: none;
    overflow-y: unset;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  </style>
