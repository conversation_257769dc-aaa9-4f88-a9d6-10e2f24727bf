<template>
    <div class="main-create">
      <!-- <img class="create-btn" src="https://img10.360buyimg.com/imgzone/jfs/t1/254324/13/8128/1772/6778a6d2F8ad24caf/6a0cee3fe743c635.png"/> -->
      <div class="upload-view">
        <div class="upload-view" style="margin: unset" :style="{backgroundImage: `url(${editInfo.workImageUrl})`}"></div>
      </div>
      <div class="edit-box">
          <!-- <div class="edit-label">标题:</div> -->
          <div class="title-view">
            <input placeholder="填写标题获得更多关注" type="text" v-model="editInfo.workTitle" maxlength="15"/>
          </div>
      </div>
      <div class="edit-box">
          <!-- <div class="edit-label">图片介绍:</div> -->
          <div class="content-view">
            <textarea placeholder="介绍一下您的图片吧~字数限制140字" maxlength="140" v-model="editInfo.workDescription"></textarea>
          </div>
      </div>
      <div class="edit-box">
          <div class="edit-label">选择分区:</div>
          <div class="title-view" style="background-color: unset;">
            <van-dropdown-menu>
                <van-dropdown-item disabled v-model="editInfo.categoryId" :options="option1" />
            </van-dropdown-menu>
          </div>
      </div>
      <img class="tips-img" @click="showRulePopup = true" src="https://img10.360buyimg.com/imgzone/jfs/t1/266137/33/15040/9844/6792fc25F2428e0b2/4d91dc130260920f.png">
      <div class="edit-box">
        <img style="width: 3.38rem;height: 0.83rem;" @click="publishWork" src="https://img10.360buyimg.com/imgzone/jfs/t1/260079/39/7850/3736/6778b9ecF9f81d7aa/3a1d3362a83faae0.png">
        <img style="width: 3.38rem;height: 0.83rem;" @click="showDelete = true" src="https://img10.360buyimg.com/imgzone/jfs/t1/260845/35/7531/3692/6778b9ecF370aa16e/ecf6cea999e7d5fe.png">
      </div>
      <!-- 未中奖 -->
    <VanPopup teleport="body" v-model:show="showDelete" position="center" :close-on-click-overlay="isCloseOverlay">
        <div style="width: 6.5rem;height: 8rem;position:relative;">
            <div class="delete-view" @click="showNoPrize = false">
              <div class="bottom-btn-box">
                <img style="width:2.33rem;height: 0.64rem;" @click="deleteWork" src="//img10.360buyimg.com/imgzone/jfs/t1/255822/25/9270/2319/677b3a64F0c3ff150/81439e4e7334feea.png">
                <img style="width:2.33rem;height: 0.64rem;" @click="showDelete = false" src="//img10.360buyimg.com/imgzone/jfs/t1/254815/29/9725/1693/677b3a5eF2af92078/fd02f2b5f26c1371.png">
              </div>
            </div>
            <div class="close-btn" style="bottom: 0rem;top:unset;left: 2.95rem;" @click="showDelete = false"></div>
        </div>
    </VanPopup>
    <!-- 活动规则 -->
    <VanPopup teleport="body" v-model:show="showRulePopup" position="center" :close-on-click-overlay="isCloseOverlay">
        <div style="width: 6.5rem;height: 8rem;position:relative;">
            <div class="rule-popup">
                <div class="title">活动规则</div>
                <div class="rule-content" v-html="ruleFormat(rules)">
                </div>
            </div>
            <div class="close-btn" style="bottom: 0rem;top:unset;left: 2.95rem;" @click="showRulePopup = false"></div>
        </div>
    </VanPopup>
    </div>
</template>
<script lang="ts" setup>
/* eslint-disable */
import { ref, Ref, reactive, onBeforeMount, inject, onMounted } from 'vue';
import { ruleFormat, formatDate, getMobileModel, iosFace, androidFace } from '../common';
import { httpRequest } from '@/utils/service';
import { showLoadingToast, showToast, closeToast, DropdownMenu, DropdownItem } from 'vant';
import { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import CropperPopup from '../components/Cropper';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
const pathParams: any = inject('pathParams');
const editInfo: any = ref(inject('editInfo', null) as any);
const category: any = inject('category');
const showDelete = ref(false);
const showRulePopup = ref(false);
const rules = inject('rules');

const title = ref('');
const contentText = ref('');

type DefineEmits = {
  (e: 'toggle-component', componentName: string): void;
};
const emits = defineEmits<DefineEmits>();
    const option1 = ref([]);
/**
 * 选择照片
 */
const needHandleImageSrc = ref('');
const imgformatDate = ref();
const handlePhotoSelect = (): void => {
  const app = document.getElementById('container') as HTMLElement;
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/*';
  const system = getMobileModel();
  if (system === 'iPhone') {
    input.accept = 'image/jpeg,image/png,image/jpg';
  } else {
    input.accept = 'image/*';
  }
  input.style.display = 'none';
  app.appendChild(input);
  input.onchange = async (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target && target.files && target.files.length > 0) {
      const file = target.files[0];
      // 进一步处理 file
      if (file) {
        // console.log(file);
        let formData = new FormData();
        formData.append('file', file);
        formData.append('pictureCateId', 123123);
        imgformatDate.value = formData;

        // console.log(file, '裁剪前');
        handleAvatarValidate(file);
      }
    }
  };
  input.click();
};
const publishWork = async () => {
  try {
  if (editInfo.value.workTitle === '') {
    showToast('标题不能为空');
    return;
  }
  const titleTexttrim = editInfo.value.workTitle.trim();
  if (titleTexttrim === null || titleTexttrim === '' || titleTexttrim === undefined) {
    showToast('标题不能为纯空格');
    return;
  }
  if (editInfo.value.workDescription === '') {
    showToast('图片介绍不能为空');
    return;
  }
  const contentTexttrim = editInfo.value.workDescription.trim();
  if (contentTexttrim === null || contentTexttrim === '' || contentTexttrim === undefined) {
    showToast('图片介绍不能为纯空格');
    return;
  }
  if (iosFace.test(editInfo.value.workTitle) || androidFace.test(editInfo.value.workTitle)) {
    showToast('标题不能含有表情');
    return;
  }
  if (iosFace.test(editInfo.value.workDescription) || androidFace.test(editInfo.value.workDescription.value)) {
    showToast('内容不能含有表情');
    return;
  }
  showLoadingToast({})
  const res = await httpRequest.post('/dz/1874673979168907266/saveWorks', editInfo.value, {});
    console.log(res.data);
  if (res.message) {
    showToast(res.message)
  } else {
    emits('toggle-component', 'Info');
  }
} catch (error: any) {
  showToast(error.message);
  }finally {
      closeToast();
  }
};
const deleteWork = async () => {
  try {
    const res = await httpRequest.post('/dz/1874673979168907266/deleteWorks', {workId: editInfo.value.id}, {});
    if (res.message) {
    showToast(res.message)
  } else {
    emits('toggle-component', 'Info');
  }
  } catch (error: any) {
    showToast(error.message);
  }
};
const uploadImg = async () => {
    const config = {
        headers: {
        'Content-Type': 'multipart/form-data',
        },
    };
    const res = await httpRequest.post('/common/uploadImg', imgformatDate.value, config);
    console.log(res);
};
const isShowCropperPopup: Ref<boolean> = ref(false);
// 处理头像校验
const handleAvatarValidate = (file: File) => {
  const reader = new FileReader();
  reader.onload = (e) => {
    const dataUrl = e.target?.result;
    const image = new Image();
    image.onload = () => {
      needHandleImageSrc.value = image.src;
      // 开始裁剪
    //   isShowCropperPopup.value = true;
    };
    if (typeof dataUrl === 'string') {
      image.src = dataUrl;
    }
  };
  reader.readAsDataURL(file);
};
const init = () => {
  const temp = [];
  category.value.forEach(element => {
    const obj = {
      text: element.categoryTitle,
      value: element.id
    }
    temp.push(obj)
  });
  option1.value = temp;
}
onMounted(() => {
  console.log(editInfo.value);
  init();
});

</script>
<style lang="scss">
.rule-popup {
    width: 6.5rem;
    height: 7.15rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/263773/16/7543/62031/6777d6a9Fb25963df/e52ac400bdfa34b8.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    padding-top: 0.2rem;
    .title {
        width: 6.5rem;
        text-align: center;
        font-size: 0.4rem;
        color: #fff;
        font-weight: bold;
        margin-bottom: 0.35rem;
    }
    .rule-content {
        width: 6rem;
        margin-left: 0.25rem;
        max-height: 5.75rem;
        overflow-y: scroll;
        font-size: 0.21rem;
        color: #fff;
    }
}
.delete-view {
    width: 6.5rem;
    height: 7.15rem;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/264238/21/7943/106063/677b3a6cFf53deff4/1cdf2e41f40ab753.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    padding-top: 5.05rem;

    .bottom-btn-box {
      width: 6.5rem;
      display: flex;
      justify-content: space-around;
    }
}

.van-dropdown-menu {
    height: 100%;
    width: 2.35rem;
    .van-dropdown-menu__bar {
        height: 100%;
        // width: 100%;
        background-color: #fff;
        border: unset;
        border-radius: 0.1rem;
    }
    .van-dropdown-menu__title {
      width: 80%;
    }
    // .van-dropdown-menu__title:after {
    //   background-color: #e5432f
    // }
    .van-ellipsis {
        font-size: 0.25rem;
    }
}
.main-create {
    width: 7.5rem;
    height: 100vh;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/266725/39/7606/135680/6777e216Ff08a8bbb/fae0174d6a39ca12.png');
    background-repeat: no-repeat;
    background-size: 7.5rem auto;
    background-position: top;
    position: relative;
    box-sizing: border-box;
    background-color: #e64330;
    box-sizing: border-box;
    padding-top: 1.3rem;
}
.create-btn {
    width: 7.5rem;
    height: 0.96rem;
    margin-bottom: 0.35rem;
}
.upload-view {
    width: 6.92rem;
    height: 3.5rem;
    margin-left: 0.24rem;
    margin-bottom: 0.85rem;
    background-image: url('https://img20.360buyimg.com/imgzone/jfs/t1/253903/7/6607/11941/6778a861Fa3f05539/d87cc33ecbd2694b.png');
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
}
.edit-box {
    width: 6.73rem;
    height: auto;
    margin-left: 0.385rem;
    margin-bottom: 0.385rem;
    display: flex;
    justify-content: space-between;
    .edit-label {
        // width: 1.35rem;
        margin-right: 0.15rem;
        flex: none;
        // text-align: right;
        font-size: 0.28rem;
        line-height: 0.6rem;
        color: #fff;
    }
}
.title-view {
  width: 6.73rem;
  height: 0.6rem;
  border-radius: 0.1rem;
  background-color: #fff;
  display: flex;
  align-items: center;

  input {
    width: 100%;
    background: unset;
    border: unset;
    font-size: 0.2rem;
    font-weight: bold;
    color: #000;
  }
}
.content-tips {
  font-size: 0.15rem;
  color: #999;
  width: 5.3rem;
  text-align: right;
}
.content-view {
    width: 6.73rem;
    height: 2.3rem;
    border-radius: 0.1rem;
    background-color: #fff;

  textarea {
    width: 100%;
    height: 100%;
    border: unset;
    background: unset;
    font-size: 0.2rem;
    font-weight: bold;
    color: #000;
  }
}
.tips-img {
  width: 5.05rem;
  height: 1.16rem;
  margin-top: 0.37rem;
  margin-bottom: 0.37rem;
  margin-left: 1.225rem;
}
.join-btn {
    width: 6.78rem;
    height: 0.83rem;
    margin-left: 0.36rem;
}
</style>
