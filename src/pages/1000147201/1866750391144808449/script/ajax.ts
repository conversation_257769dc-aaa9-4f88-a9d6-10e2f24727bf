import { httpRequest } from '@/utils/service';
import { showToast, showLoadingToast, closeToast } from 'vant';

export interface Form {
  month: string | number | null;
  monthText: string;
  phone: string;
  verCode: string;
  pageNum: number;
  pageSize: number;
}
export interface PrizeItem {
  activityName: string;
  address: string;
  addressStatus: boolean;
  city: string;
  county: string;
  district: string;
  infoId: string;
  phone: string;
  prizeName: string;
  prizeTime: number;
  prizeType: number;
  province: string;
  receiver: string;
  shippingMsg: string;
  shippingStatus: number;
  shippingStatusInfo: string;
}
export const writeAddress = async (form: any) => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { code, data, message } = await httpRequest.post('/swisse/delivery/updateAddress', {
      ...form,
    });
    closeToast();
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return true;
  } catch (error: any) {
    closeToast();
    showToast(error?.message);
    console.error(error);
    return false;
  }
  return false;
};
export const getMyPrize = async ({ verCode, month, pageNum, pageSize, phone }: Form) => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/swisse/delivery/getInfoPage', {
      code: verCode,
      month, // 1-12
      pageNum,
      pageSize,
      phone,
    });
    closeToast();
    return data || {};
  } catch (error: any) {
    closeToast();
    showToast(error?.message);
    return {};
  }
};
export const sendIdentifyCode = async (phone: string) => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { code, data, message } = await httpRequest.post('/common/sendIdentifyCode', { phone });
    closeToast();

    if (code !== 200) {
      showToast(message);
      return false;
    }
    return true;
  } catch (error: any) {
    closeToast();
    showToast(error?.message);
    return false;
  }
  return false;
};
export const getShippingMsgByInfoId = async (infoId: string) => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { code, data, message } = await httpRequest.post('/swisse/delivery/getShippingMsgByInfoId', {
      infoId,
    });
    closeToast();

    if (code !== 200) {
      showToast(message);
      return false;
    }
    return data || {};
  } catch (error: any) {
    closeToast();
    showToast(error?.message);
    return {};
  }
  return {};
};
