// sku 接口类型
export interface Skus {
  name?: string;
}

// res 接口类型
export interface Res {
  data?: any,
  code?: number,
  result?: boolean,
  message?: string
}

// api 接口类型
export interface ApisOpts {
  getActivityConfig: string,
  drawGift: string,
  activityContent: string,
  editAddress: string,

  [propname: string]: any,
}

export interface IStep {
  image: string,
  skuId: string,
}

export interface IConfigData {
  homeKv: string,
  actFlowImage: string,
  step1: IStep[],
  step2: IStep[],
  step3: IStep[],
}

export interface ICombinedGifts {
  combinedId: number,
  image: string,
  status: string,
  subId: string,
}

export interface IGoods {
  image: string,
  skuId: string,
}

export interface IActions {
  subId: number,
  actionSort: number,
  repurchaseOrder: number,
  step1Topic: string,
  step2Topic: string,
  combinedGifts: ICombinedGifts[],
  goods: IGoods[],
}

export interface ImyOrders {
  orderDate: string,
  orderId: number,
  orderStatus: string,
}

export interface IMyPrizes {
  address: string,
  city: string,
  county: string,
  mobile: string,
  province: string,
  recordDate: string,
  subName: string,
  receiverName: string,
  rightsName: string,
  receiveStatus: number,
  recordId: number,
  rightsType: number,
}

export interface IProgress {
  isReach: boolean,
  topName: string,
  topTips: string,
  topImage: string,
}

export interface IButtons {
  combinedId: number,
  image: string,
  status: string,
  subId: string,
  subName: string,
}

export interface IBottoms {
  bottomName: string,
  bottomStatus: string,
  subId: number,
  buttons: IButtons[],
}

export interface IActivityData {
  rules: string,
  actions: IActions[],
  myOrders: ImyOrders[],
  myPrizes: IMyPrizes[],
  bottoms: IBottoms[],
  progress: IProgress[],
}
