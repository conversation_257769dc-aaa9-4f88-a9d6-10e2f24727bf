<template>
  <div class="clock-days-container">
    <div class="clock-days">
      <div class="clock-days-item">当月最大连续打卡天数：{{ signDetail.contiTimes }}天</div>
      <div class="progress-bar">
        <div class="progress-segment" v-for="(segment, index) in signDetail.signActivitySignScoreVos" :key="index">
          <div class="progress-icon-container">
            <img :src="getSegmentImage(segment)" alt="" :class="['progress-icon', { completed: signDetail.contiTimes >= segment.contiSignTimes }]" />
          </div>
          <div class="segment-num">连续打卡{{ segment.contiSignTimes }}次</div>
          <div class="segment-text">{{ `${pointName}+${segment.giveScore}` }}</div>
        </div>
        <div class="segment-progress">
          <div class="segment-progress-bar" :style="{ width: getProgressWidth }"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, reactive, ref, computed, PropType } from 'vue';
import pendingImage from '../assets/pendingImage.png';
import completedImage from '../assets/completedImage.png';
import type { SignInDetailType, SignScoreVos } from '../scripts/types.ts';

const pointName = inject('pointName');

const props = defineProps({
  signDetail: {
    type: Object as PropType<SignInDetailType>,
    required: true,
    default: () => ({}),
  },
});
const getSegmentImage = (segment: SignScoreVos) => (props.signDetail.contiTimes >= segment.contiSignTimes ? completedImage : pendingImage);
const getProgressWidth = computed(() => {
  // 进度条总长度（单位：rem）
  const totalWidth = 2.5;
  // 总分段数 = 分段节点数 - 1
  //  （例如 5个节点形成4个分段）
  const total = props.signDetail.signActivitySignScoreVos.length - 1;
  // 计算每个分段的固定宽度
  const stepWidth = Number((totalWidth / total).toFixed(2));

  // 边界情况1：用户数小于等于初始值时进度为0
  if (props.signDetail.contiTimes <= props.signDetail.signActivitySignScoreVos[0]?.contiSignTimes) {
    return '0rem';
  }
  // 边界情况2：用户数超过最大需求值时进度拉满
  if (props.signDetail.contiTimes >= props.signDetail.signActivitySignScoreVos[total]?.contiSignTimes) {
    return `${totalWidth}rem`;
  }

  // 找到第一个超过当前用户数的分段节点
  const maxIndex1 = props.signDetail.signActivitySignScoreVos.findIndex((segment: SignScoreVos) => segment.contiSignTimes > props.signDetail.contiTimes);
  // 确定用户当前所在的分段（前一个节点）
  const maxIndex = maxIndex1 - 1;
  // 获取当前分段的基准数据
  const segment = props.signDetail.signActivitySignScoreVos[maxIndex];

  // 计算当前分段的数值范围
  const nextNeedNum = props.signDetail.signActivitySignScoreVos[maxIndex + 1]?.contiSignTimes ?? 0;
  const stepSegmentNum = Math.abs(segment?.contiSignTimes - nextNeedNum);

  // 计算进度组成：
  // 1. 前序分段的固定宽度总和：步长 * 已完成的段数
  // 2. 当前分段的动态比例：当前进度占分段的百分比 * 步长
  const userSegmentWidth = Number(((props.signDetail.contiTimes - segment?.contiSignTimes) / stepSegmentNum) * stepWidth) + Number(stepWidth * maxIndex);

  // 返回最终进度值（保留两位小数 + rem单位）
  return `${userSegmentWidth.toFixed(2)}rem`;
});
</script>
<style scoped lang="scss">
.clock-days-container {
  position: relative;
  border: 1px solid #ffffff;
  padding: 0.1rem 0.15rem;
  background-color: #1d7358;
  color: #fff;
  .clock-days {
    .clock-days-item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      font-size: 0.15rem;
      font-weight: 700;
    }
  }
}
.progress-bar {
  display: flex;
  justify-content: space-between;
  width: 100%;
  position: relative;
}

.progress-segment {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  inset: 0;
  z-index: 1;
}
.segment-progress {
  position: absolute;
  left: 0.3rem;
  top: 0.2rem;
  z-index: 0;
  width: 2.5rem;
  height: 0.06rem;
  background-color: #d7d7d7;
  border-radius: 0.03rem;
  .segment-progress-bar {
    background-color: #6d0bfa;
    position: absolute;
    height: 0.07rem;
    background-image: linear-gradient(180deg, #00d8ff 0%, #1982ff 100%);
    border-radius: 0.03rem;
  }
}
.progress-icon-container {
  width: 0.8rem;
  height: 0.48rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.progress-icon {
  width: 0.35rem; /* 图标宽度 */
  height: 0.35rem; /* 图标高度 */
  object-fit: contain; /* 确保图标不会变形 */
}
.completed {
  width: 0.35rem; /* 图标宽度 */
  height: 0.35rem; /* 图标高度 */
}
.segment-num {
  font-size: 0.1rem;
}
.segment-text {
  margin-top: 0.02rem;
  font-size: 0.1rem;
  color: #000;
  background-color: #ffffff;
  border-radius: 0.03rem;
  line-height: 1;
  padding: 0.02rem 0.1rem;
  box-sizing: border-box;
}

/* 若需要引入不同状态的样式，可增加特定的状态样式，如 hover 状态等 */
.progress-segment:hover {
  background-color: rgba(0, 0, 0, 0.1); /* 悬浮变色 */
}
</style>
