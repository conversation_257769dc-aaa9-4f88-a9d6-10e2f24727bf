<template>
  <popup teleport="body" v-model:show="isShowPopup" :close-on-click-overlay="false" @closed="handleClose" @opened="handleOpen">
    <div class="box">
      <div class="title">填写地址</div>
      <div class="row">
        <div>收货人：</div>
        <input type="text" v-model="form.realName" placeholder="请输入收货人" maxlength="10" />
      </div>
      <div class="row">
        <div>手机号：</div>
        <input type="text" v-model="form.mobile" placeholder="请填写手机号" oninput="value=value.replace(/[^\d]/g,'')" maxlength="11" />
      </div>
      <div class="row">
        <div>所在地区：</div>
        <input type="text" v-model="form.addressCode" readonly placeholder="选择省/市/区" @click="addressSelects = true" />
      </div>
      <div class="row">
        <div>详细地址：</div>
        <input type="text" v-model="form.address" placeholder="请填写详细地址" maxlength="30" />
      </div>
      <!-- 确认提交 -->
      <div class="submit-btn" @click="checkForm">提交收货地址</div>
      <Icon name="close" size="40" class="close-icon" @click="handleClose"></Icon>
    </div>
    <!--地址选择-->
    <Popup v-model:show="addressSelects" teleport="#app" position="bottom">
      <VanArea title="请输入详细地址" :area-list="areaList" @confirm="confirmAddress" @cancel="onCancel" />
    </Popup>
  </popup>
</template>

<script setup lang="ts">
import { computed, defineEmits, defineProps, inject, ref, reactive, watch, watchEffect } from 'vue';
import { Popup, Icon, Area, showToast } from 'vant';
import { areaList } from '@vant/area-data';
import { containsEmoji, containsSpecialChars, isPhoneNumber, validateDataWithRules } from '@/utils/platforms/validator';
import { setAddress, getAddress } from '../scripts/ajax';

const props = defineProps({
  showPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  addressId: {
    type: String,
    default: '',
  },
  id: {
    type: String,
    default: '',
  },
});
const isShowPopup = computed(() => props.showPopup);
const emits = defineEmits(['closeDialog']);
const form = reactive({
  realName: '',
  mobile: '',
  province: '',
  county: '',
  city: '',
  address: '',
  addressCode: '',
});
const handleClose = () => {
  Object.assign(form, {
    realName: '',
    mobile: '',
    province: '',
    county: '',
    city: '', // 区
    address: '',
    addressCode: '',
  });
  emits('closeDialog', false);
};
const address = ref('');

const addressSelects = ref(false);
// 关闭三联地址框
const onCancel = () => {
  addressSelects.value = false;
};
// 确认三联地址信息
const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList?.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
  form.addressCode = addressItemList.selectedOptions.map((item: any) => item.text).join('/');
};
const ruleValidate = {
  realName: [
    {
      required: true,
      message: '请输入收货人',
    },
    {
      validator: containsSpecialChars,
      message: '收货人不能包含特殊字符',
    },
    {
      validator: containsEmoji,
      message: '收货人不能包含表情',
    },
  ],
  mobile: [
    {
      required: true,
      message: '请输入手机号',
    },
    {
      validator: isPhoneNumber,
      message: '请输入正确的手机号',
    },
  ],
  province: [
    {
      required: true,
      message: '请选择省/市/区',
    },
  ],
  address: [
    {
      required: true,
      message: '请输入详细地址',
    },
    {
      validator: containsEmoji,
      message: '详细地址不能包含表情',
    },
  ],
};
const checkForm = async () => {
  const valid = validateDataWithRules(ruleValidate, form);
  if (!valid) return;
  const res = await setAddress({ ...form, addressId: props.addressId });
  if (res) {
    showToast('保存成功~');
    emits('closeDialog', true);
  }
};
const handleOpen = async () => {
  if (props.id && props.id !== '') {
    const info = await getAddress(props.id);
    if (!info.mobile) return;
    Object.assign(form, info);
    form.addressCode = `${form.province}/${form.city}/${form.county}`;
  }
};
</script>
<style lang="scss" scoped>
.box {
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/266602/26/24474/159566/67bdae74F8185e740/1b6f1542f76ae360.png) no-repeat;
  background-size: contain;
  width: 6.5rem;
  height: 10rem;
  padding: 0.13rem 0.4rem;
  box-sizing: border-box;
  position: relative;
  .title {
    margin-left: 0.91rem;
    font-size: 0.48rem;
    color: #000;
    font-weight: bold;
  }
  .row {
    display: flex;
    align-items: center;
    padding: 0.2rem 0.2rem 0.15rem;
    margin-top: 0.32rem;
    color: #fff;
    font-size: 0.3rem;
    div {
      min-width: 1.5rem;
      text-align: right;
    }
    input {
      flex: 1;
      border: none;
      background: #fff;
      color: #000;
      padding: 0.15rem 0 0.2rem 0.4rem;
      box-sizing: border-box;
    }
  }
  .submit-btn {
    background: url(../assets/btn-bg.png) no-repeat;
    background-size: 100% 100%;
    width: 3.34rem;
    height: 0.8rem;
    text-align: center;
    line-height: 0.8rem;
    font-weight: bold;
    font-size: 0.37rem;
    color: #000;
    margin: 0.2rem auto 0;
  }
  .warning {
    font-size: 0.18rem;
    text-align: center;
  }
}
.mask {
  position: absolute;
  inset: 0;
  background-color: transparent;
  z-index: 1;
}
.close-icon {
  position: absolute;
  width: 1rem;
  height: 1rem;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
}
</style>
