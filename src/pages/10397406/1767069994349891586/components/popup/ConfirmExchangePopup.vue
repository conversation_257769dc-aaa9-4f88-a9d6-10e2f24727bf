<template>
  <van-popup class="my-popup" :show="show">
    <div class="popup-container animate__animated animate__faster" :style="{ backgroundImage: `url(${jsonData.confirmExchangePopupBgImg}})` }">
      <div class="popup-content" :style="{ color: `${jsonData.confirmExchangePopupTextColor}` }">
        <div class="prize-info">
          <div class="prize-info-top">
            <span class="text-1" :style="{ borderColor: `${jsonData.confirmExchangePopupTextColor}` }">是否消耗</span>
            <span class="text-2">{{ exchangeData.exchangePoints }}积分</span>
            <span class="text-2">兑换奖品</span>
          </div>
          <div class="handle-button-container">
            <img @click="emits('handlePrize')" class="handle-button-img" :src="jsonData.confirmExchangePopupButtonImg" alt="" />
          </div>
        </div>
      </div>
      <button class="close-popup-btn" @click="show = false"></button>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineExpose, inject, PropType } from 'vue';
// import {num} from "video.js";
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { configData } from '../../common';

// 兑换结果
const winPrizePopup = ref();
const drawResInfo = reactive({
  value: {},
});
// 配置数据
const moduleName = 'PointExchange';
const { jsonData } = configData.value[moduleName];

const show = ref(false);
const props = defineProps({
  exchangeData: {
    type: Object as PropType<any>,
    required: true,
  },
});

const emits = defineEmits(['handlePrize']);

// 显示暴露的数据，才可以在父组件拿到
defineExpose({
  show,
});
</script>

<style lang="scss" scoped>
.popup-content {
  display: flex;
  justify-content: center;
  width: 6.21rem;
  height: 7.49rem;
  padding: 1.5rem 0.9rem 0.7rem;
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100% auto;
}

.prize-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  font-family: 'FZPSHJW--GB1-0';
}

.prize-info-top {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: space-evenly;
  padding: 0.6rem 0;

  .text-1 {
    padding: 0.08rem 0.24rem;
    font-size: 0.3rem;
    border-style: dashed;
    border-width: 1px;
    border-radius: 0.2rem;
  }

  .text-2 {
    font-size: 0.62rem;
  }
}

.handle-button-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 1rem;
  margin-bottom: 0.3rem;
}

.handle-button-img {
  width: 1.92rem;
  height: 0.57rem;
}
</style>
<style lang="scss">
.rule-popup.van-popup {
}
</style>
