<!--
 * @actName:
 * @author: <PERSON><PERSON><PERSON> lin
-->
<template>
  <!-- background -->
  <div class="background">

    <template v-if="activityData.flag === '0'">
      <div class="search-view">
        <input class="search-input" placeholder="输入序号/名称" maxlength="10" v-model="searchInput">
        <div class="search-btn" @click="searchPet()"></div>
      </div>

      <div class="vote-view">
        <div class="vote-data" ref="listRef" :style="{'height':visibleItems?.length>6?'11.4rem':''}" v-if="visibleItems?.length>0">
          <div class="vote-item-view" v-for="(item,index) in visibleItems" :key="index">
            <div class="vote-item">
              <img class="vote-image" :src="getPetImage(item.image)" alt="">
              <div class="pet-number text-shadow">{{ item.orderVal }}号</div>
              <div class="pet-name one-line-omit">{{ item.name }}</div>
              <div class="pet-voted">票数:{{ item.voteNum }}</div>
            </div>
            <div class="vote-btn" :class="{'gray':activityData.drawCount===0}" @click="votePet(item.id)"></div>
          </div>
        </div>
        <div v-else class="none-data-tip">暂无宠物数据</div>
      </div>

      <img src="./assets/img/rule-img.png" class="rule-img" alt="">
    </template>

    <template v-else>
      <div class="rank-view">
        <div class="rank-data" v-if="petData.length>0">
          <div class="rank-item-view" v-for="(item,index) in petData" :key="index">
            <div class="rank-item">
              <img class="rank-image" :src="getPetImage(item.image)" alt="">
              <img class="rank-number" :src="getRankIcon(index+1)" alt=""/>
              <div class="pet-number text-shadow">{{ item.orderVal }}号</div>
              <div class="pet-name one-line-omit">{{ item.name }}</div>
              <div class="pet-voted text-shadow">票数:{{ item.voteNum }}</div>
            </div>
            <div class="rank-btn">TOP{{ index + 1 }}</div>
          </div>
        </div>
        <div v-else class="none-data-tip">暂无宠物数据</div>
      </div>
    </template>

  </div>

  <Popup class="popup" v-model:show="dialogShow" :close-on-click-overlay="false">
    <VoteTipDialog :voteNumber="activityData.drawCount"></VoteTipDialog>
  </Popup>

</template>

<script lang='ts' setup>
import { ref, inject, Ref, onMounted, reactive, nextTick } from 'vue';
import { Toast, Popup, showToast } from 'vant';
import { BaseInfo } from '@/types/BaseInfo';
import { callShare, setHeaderShare } from '@/utils/platforms/share';

/* ---------------------------------  弹窗  ------------------------------ */
import VoteTipDialog from './components/VoteTipDialog.vue';
import { openDialog, dialogShow } from './ts/dialog';
/* ---------------------------------  接口  ------------------------------- */
import { getDataInterface, setBaseInfo } from './ts/port';
import type { IActivityData } from './ts/type';
import constant from '@/utils/constant';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
setBaseInfo(baseInfo);

// const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
setHeaderShare({
  title: '京东生活小家电宠爱生活新主张',
  content: '京东生活小家电宠爱生活新主张',
  imageUrl: '',
});

const listRef = ref(null);
const canLoadMore = ref(true);
const searchInput = ref();
const petData = ref([]);
const visibleItems = ref();

const getRankIcon = (rank: number) => {
  switch (rank) {
    case 1:
      return '//img10.360buyimg.com/imgzone/jfs/t1/230283/2/25066/20458/66d95c86F0742be9c/0e59aac79d763765.png';
    case 2:
      return '//img10.360buyimg.com/imgzone/jfs/t1/132693/3/44769/20907/66d95c85F963ae954/e47df89f9c7766d3.png';
    case 3:
      return '//img10.360buyimg.com/imgzone/jfs/t1/231418/11/25999/15400/66d95c84F658623a4/464ac9670bf4e370.png';
    case 4:
      return '//img10.360buyimg.com/imgzone/jfs/t1/87310/24/48502/16588/66d95c85Fba0af070/3fe6c2b07141d986.png';
    case 5:
      return '//img10.360buyimg.com/imgzone/jfs/t1/149372/25/42359/16849/66d95c85Fb9972c42/dc46f80a8b800936.png';
    default:
      return '';
  }
};

const getPetImage = (image: string) => {
  if (image.includes('.png') || image.includes('.jpg')) {
    return image;
  }
  return '//img10.360buyimg.com/imgzone/jfs/t1/128370/36/39988/2024/64fa7eedF35ed77d0/f6f4b0dfa6a5c977.png';
};

// 加载更多数据
const loadMore = () => {
  const lastVisibleIndex = visibleItems.value.length ?? 0;
  const nextBatch = petData.value.slice(lastVisibleIndex, lastVisibleIndex + 10);
  if (nextBatch.length === 0) {
    canLoadMore.value = false;
  }
  visibleItems.value.push(...nextBatch);
};

const checkLoadMore = () => {
  const listElement = listRef.value;
  const { scrollTop, clientHeight, scrollHeight } = listElement;

  if (scrollTop + clientHeight >= scrollHeight - 50 && canLoadMore.value) {
    loadMore();
  }
};

const activityData = reactive({}) as IActivityData;
// 主接口
const activityContent = async () => {
  const data = await getDataInterface('activityContent');
  console.log(data);
  if (data.result) {
    Object.assign(activityData, data.data);
    if (activityData.flag === '0') {
      petData.value = activityData.pageVO.list;

      if (petData.value.length > 10) {
        visibleItems.value = petData.value.slice(0, 10);
        await nextTick(() => {
          listRef.value.addEventListener('scroll', checkLoadMore);
        });
      }

    } else if (activityData.flag === '1') {
      petData.value = activityData.rankVO.rankList ?? [];
    }
  } else {
    showToast(data.errorMessage);
  }
};

// 查询接口
const searchPet = async () => {
  const data = await getDataInterface('petSearch', 'post', { petInfo: searchInput.value });
  if (data.result) {
    console.log(data.data);
    if (searchInput.value?.length > 0) {
      petData.value = [];
      visibleItems.value = data.data ?? [];
    } else {
      canLoadMore.value = true;
      visibleItems.value = data.data.slice(0, 10);
      petData.value = data.data ?? [];
    }
  } else {
    showToast(data.errorMessage);
  }
};

// 投票接口
const votePet = async (petInfo: string) => {
  if (activityData.drawCount > 0) {
    const data = await getDataInterface('petVote', 'post', { petInfo });
    if (data.result) {
      if (searchInput.value) {
        searchInput.value = '';
      }
      await activityContent();
      openDialog('');
    } else {
      showToast(data.errorMessage);
    }
  }
};

// 监听滚动事件
// onMounted(() => {
// });

activityContent();

const skuList = ref([]) as any;
onMounted(async () => {
  // skuList.value = await findSkuInfo(baseInfo.activityMainId);
});

</script>

<style lang='scss'>
.gray {
  /*grayscale(val):val值越大灰度就越深*/
  -webkit-filter: grayscale(100%) brightness(1);
  -moz-filter: grayscale(100%) brightness(1);
  -ms-filter: grayscale(100%) brightness(1);
  -o-filter: grayscale(100%) brightness(1);
  filter: grayscale(100%) brightness(1);
  filter: gray brightness;
}

#app .van-popup {
  background-color: transparent;
}
</style>
