<template>
  <div id="container">
    <Transition enter-active-class="animate__animated animate__fadeIn" mode="out-in">
      <component :is="curComponent" @toggle-component="handleToggleComponent"></component>
    </Transition>
    <ReturnHomeBtn v-if="curComponent!==Home" @toggle-component="handleToggleComponent" />
  </div>
</template>

<script lang="ts" setup>
/* eslint-disable */
/* 快捷打开项目命令  npm run serve src/pages/734259/1856635416454815745 */
/* 测试链接 https://lzkjdz-isv.isvjcloud.com/test/cc/custom/734259/1856635416454815745 */
/*
* 本项目为京东API调研工作，不定时更新最新的调研结果
* */

import { inject, shallowRef } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import Home from './views/Home.vue';
import CustomScreenshots from './views/CustomScreenshots.vue';
import ReturnHomeBtn from './views/ReturnHomeBtn.vue';
import Barrage from './views/Barrage.vue';

const componentList = {
  Home,
  CustomScreenshots,
  Barrage,
};
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
const pathParams: any = inject('pathParams');
const baseUserInfo: any = inject('baseUserInfo');

// 当前显示的组件
const curComponent = shallowRef(Home);

// 处理切换组件
const handleToggleComponent = (componentName: string) => {
  curComponent.value = componentList[componentName];
};

</script>

<style lang="scss" scoped>
#container {
  width: 100vw;
  min-height: 100vh;
  max-width: 100vw;
  line-height: 1;
  margin-bottom: 0.2rem;
}
</style>

<style lang="scss">
/* 隐藏Webkit内核浏览器的滚动条 */
::-webkit-scrollbar {
  display: none;
}

// 禁止页面回弹行为
html,
body {
  overscroll-behavior-y: none;
  overscroll-behavior-x: none;
}

.popup.van-popup {
  background: none;
  overflow-y: unset;
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
