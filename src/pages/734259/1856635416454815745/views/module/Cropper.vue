<template>
  <div id="cropper">
    <van-popup :lock-scroll="false" :show="isShow" safe-area-inset-bottom safe-area-inset-top teleport="body" @close="handleClose">
      <cropper
        :auto-zoom="false"
        :src="imageSource"
        :stencil-props="{
          aspectRatio: aspectRatio,
          class: 'cropper-stencil',
          previewClass: 'cropper-stencil__preview',
          draggingClass: 'cropper-stencil--dragging',
          handlersClasses: {
            default: 'cropper-handler',
            eastNorth: 'cropper-handler--east-north',
            westNorth: 'cropper-handler--west-north',
            eastSouth: 'cropper-handler--east-south',
            westSouth: 'cropper-handler--west-south',
          },
          handlersWrappersClasses: {
            default: 'cropper-handler-wrapper',
            eastNorth: 'cropper-handler-wrapper--east-north',
            westNorth: 'cropper-handler-wrapper--west-north',
            eastSouth: 'cropper-handler-wrapper--east-south',
            westSouth: 'cropper-handler-wrapper--west-south',
          },
        }"
        class="cropper"
        @change="handleCropperChange" />
      <div class="upload-btn" @click="handleUploadImage">
        <van-button type="primary">完成上传</van-button>
      </div>
    </van-popup>
  </div>
</template>

<script lang="ts" setup>
/* eslint-disable */
import { ref, toRefs, Ref } from 'vue';
import { httpRequest } from '@/utils/service';
import { closeToast, showToast, showLoadingToast } from 'vant';
import { Cropper } from 'vue-advanced-cropper';
import Compressor from 'compressorjs';
import 'vue-advanced-cropper/dist/style.css';

interface Props {
  imageSource: string;
  isShow: boolean;
  aspectRatio: number;
}

const props = withDefaults(defineProps<Props>(), {
  imageSource: 'https://img10.360buyimg.com/imgzone/jfs/t1/160464/30/47276/23712/66de97c2F3fc49a94/9622487457ec9f29.png',
  isShow: false,
  aspectRatio: 1,
});
type DefineEmits = {
  (e: 'close-popup', type: string): void;
  (e: 'image-data', src: string): void;
};
const emits = defineEmits<DefineEmits>();

const {
  imageSource,
  isShow,
  aspectRatio,
} = toRefs(props);

const handleClose = () => {
  emits('close-popup', 'cropper');
};
/*
* 裁剪
* */
const fileBlob = ref<Blob>();
const handleCropperChange = (data: any): void => {
  const { canvas } = data;
  handleCompressor2(canvas);
};
// 质量大小转换
const prettySize = (size: number) => {
  let kilobyte = 1024;
  let megabyte = kilobyte * kilobyte;

  if (size > megabyte) {
    return (size / megabyte).toFixed(2) + 'MB';
  } else if (size > kilobyte) {
    return (size / kilobyte).toFixed(2) + 'KB';
  } else if (size >= 0) {
    return size + 'B';
  }
  return 'N/A';
};
// 压缩
const handleCompressor2 = (canvas: HTMLCanvasElement) => {
  canvas.toBlob(
    (blob: Blob | null) => {
      if (!blob) {
        console.error('Failed to generate blob from canvas');
        return;
      }
      console.log('裁剪前', `${prettySize(blob.size)}，type：${blob.type}`);
      // 使用FileReader来读取Blob
      const reader = new FileReader();
      reader.onload = () => {
        new Compressor(blob, {
          quality: 0.9,
          success: res => {
            console.log('裁剪后', `${prettySize(res.size)}，type：${res.type}`);
            fileBlob.value = res;
          },
        });
      };
      // 读取Blob为DataURL
      reader.readAsDataURL(blob);
    },
    'image/webp',
    1,
  );
};

const compressorQuality: Ref<number> = ref(1);
// 处理裁剪后的压缩
const handleCompressor = (canvas: any) => {
  canvas.toBlob(
    (blob: Blob) => {
      console.log('裁剪前', `${prettySize(blob.size)}，type：${blob.type}`);
      // 使用FileReader来读取Blob
      const reader = new FileReader();
      reader.onload = () => {
        if (blob.size >= 1024 * 1024) {
          compressorQuality.value = Number((compressorQuality.value - 0.1).toFixed(1));
          handleCompressor(canvas);
        } else {
          console.log(blob.size, '裁剪成功');
          console.log(compressorQuality.value, '此时的质量');
          fileBlob.value = blob;
          compressorQuality.value = 1;
        }
      };
      // 读取Blob为DataURL，同时指定图片格式和质量
      reader.readAsDataURL(blob);
    },
    'image/jpeg',
    compressorQuality.value,
  );
};
// 上传图片到图片空间
const handleUploadImage = async (): Promise<any> => {
  const config = {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  };
  try {
    showLoadingToast({});
    let formData = new FormData();
    fileBlob.value && formData.append('file', fileBlob.value, 'babyAvatar.png');
    // 注意：164840056这个id是测试店铺id，后续要切换到项目的店铺id
    formData.append('pictureCateId', '164840056');
    const res = await httpRequest.post('/common/uploadImg', formData, config);
    emits('image-data', res.data);
    handleClose();
    showToast('上传成功');
  } catch (error: any) {
    showToast(error);
  } finally {
    closeToast();
  }
};
</script>

<style lang="scss">
.cropper-stencil {
  &__preview {
    &:after,
    &:before {
      content: '';
      opacity: 0;
      transition: opacity 0.25s;
      position: absolute;
      pointer-events: none;
      z-index: 1;
    }

    &:after {
      border-left: solid 1px white;
      border-right: solid 1px white;
      width: 33%;
      height: 100%;
      transform: translateX(-50%);
      left: 50%;
      top: 0;
    }

    &:before {
      border-top: solid 1px white;
      border-bottom: solid 1px white;
      height: 33%;
      width: 100%;
      transform: translateY(-50%);
      top: 50%;
      left: 0;
    }
  }

  &--dragging {
    .cropper-stencil__preview {
      &:after,
      &:before {
        opacity: 0.7;
      }
    }
  }
}

.cropper-line {
  border-color: rgba(white, 0.8);
}

.cropper-handler-wrapper {
  width: 24px;
  height: 24px;

  &--west-north {
    transform: translate(0, 0);
  }

  &--east-south {
    transform: translate(-100%, -100%);
  }

  &--west-south {
    transform: translate(0, -100%);
  }

  &--east-north {
    transform: translate(-100%, 0);
  }
}

.cropper-handler {
  display: block;
  position: relative;
  flex-shrink: 0;
  transition: opacity 0.5s;
  border: none;
  background: white;
  height: 4px;
  width: 4px;
  opacity: 0;
  top: auto;
  left: auto;

  &--west-north,
  &--east-south,
  &--west-south,
  &--east-north {
    display: block;
    height: 16px;
    width: 16px;
    background: none;
    opacity: 0.7;
  }

  &--west-north {
    border-left: solid 2px white;
    border-top: solid 2px white;
  }

  &--east-south {
    border-right: solid 2px white;
    border-bottom: solid 2px white;
  }

  &--west-south {
    border-left: solid 2px white;
    border-bottom: solid 2px white;
  }

  &--east-north {
    border-right: solid 2px white;
    border-top: solid 2px white;
  }

  &--hover {
    opacity: 1;
  }
}
</style>
<style lang="scss" scoped>
.cropper {
  width: 6.5rem;
  height: 8rem;
  // background: #ddd;
  position: relative;
  z-index: 3000;
}

.upload-btn {
  position: absolute;
  right: 0.3rem;
  bottom: 0.3rem;
  z-index: 3001;
}
</style>
