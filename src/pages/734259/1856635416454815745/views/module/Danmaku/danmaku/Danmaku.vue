<template>
  <div ref="container" class="vue-danmaku">
    <div ref="dmContainer" :class="['danmus', { show: !hidden }, { paused: paused }]"></div>
    <slot />
  </div>
</template>

<script setup lang="ts">
/*eslint-disable*/
import { createApp, defineComponent, nextTick, render, onMounted, onBeforeUnmount, PropType, ref, toRefs, reactive, computed, h } from 'vue';
import { DanChannel, DanmuItem, DanmakuItem } from './typings/Danmaku';

/**
 * 自定义弹幕
 */
type CustomDanmu = {
  [key: string]: any;
};

/**
 * 弹幕类型
 */
type Danmu = string | CustomDanmu;

function useModelWrapper<T>(props: any, emit: Function, name = 'modelValue', translater?: Function) {
  return computed<T>({
    get: () => props[name],
    set: (value) => {
      emit(`update:${name}`, translater ? translater(value) : value);
    },
  });
}

/**
 * props类型
 */
type Props = {
  /**
   * 弹幕列表
   */
  danmus: Danmu[];
  /**
   * 轨道数量 0为最大轨道数量（撑满容器）
   */
  channels?: number;
  /**
   * 自动播放 默认true
   */
  autoplay?: boolean;
  /**
   * 循环播放 默认false
   */
  loop?: boolean;
  /**
   * 是否开启插槽 默认false
   */
  useSlot?: boolean;
  /**
   * 是否开启悬浮插槽 默认false
   */
  useSuspendSlot?: boolean;
  /**
   * 弹幕刷新频率(ms) 默认100
   */
  debounce?: number;
  /**
   * 弹幕速度（像素/秒） 默认100
   */
  speeds?: number;
  /**
   * 是否开启随机轨道注入弹幕 默认false
   */
  randomChannel?: boolean;
  /**
   * 弹幕字号（仅文本模式） 默认18
   */
  fontSize?: number;
  /**
   * 弹幕垂直间距 默认4
   */
  top?: number;
  /**
   * 弹幕水平间距 默认0
   */
  right?: number;
  /**
   * 是否开启悬浮暂停 默认false
   */
  isSuspend?: boolean;
  /**
   * 弹幕额外样式
   */
  extraStyle?: string;
};

const slots = defineSlots();
const props = withDefaults(defineProps<Props>(), {
  /**
   * 弹幕列表
   */
  danmus: () => [],
  /**
   * 轨道数量 0为最大轨道数量（撑满容器）
   */
  channels: 0,
  /**
   * 自动播放 默认true
   */
  autoplay: true,
  /**
   * 循环播放 默认false
   */
  loop: false,
  /**
   * 是否开启插槽 默认false
   */
  useSlot: false,
  /**
   * 是否开启悬浮插槽 默认false
   */
  useSuspendSlot: false,
  /**
   * 弹幕刷新频率(ms) 默认100
   */
  debounce: 100,
  /**
   * 弹幕速度（像素/秒）
   */
  speeds: 200,
  /**
   * 是否开启随机轨道注入弹幕 默认false
   */
  randomChannel: false,
  /**
   * 弹幕字号（仅文本模式） 默认18
   */
  fontSize: 18,
  /**
   * 弹幕垂直间距 默认10
   */
  top: 10,
  /**
   * 弹幕水平间距 默认10
   */
  right: 10,
  /**
   * 是否开启悬浮暂停 默认false
   */
  isSuspend: false,
  /**
   * 弹幕额外样式
   */
  extraStyle: '',
});
const { danmus, channels, autoplay, loop, useSlot, debounce, speeds, randomChannel, fontSize, top, right, isSuspend, extraStyle, useSuspendSlot } = toRefs(props);

const emit = defineEmits<{
  (e: 'list-end'): void;
  (e: 'dm-over', dm: any): void;
  (e: 'dm-out', dm: any): void;
  (e: 'play-end', index: number): void;
  (e: 'dm-click', danmu: Danmu, index: number, dom?: HTMLElement): void;
  // (e: 'update:danmus', danmus: Danmu): void;
}>();
// 容器
let container = ref<HTMLDivElement>(document.createElement('div'));
let dmContainer = ref<HTMLDivElement>(document.createElement('div'));
const containerWidth = ref(0);
const containerHeight = ref(0);
// 变量
let timer: NodeJS.Timeout;
const calcChannels = ref(0);
const danmuHeight = ref(0);
const index = ref<number>(0);
const insertIndex = ref<number>(0);
const hidden = ref(false);
const paused = ref(false);
const danChannel = ref<DanChannel>({});
const suspendList = ref<HTMLElement[]>([]);
const suspendRight = ref<number>(10);

// 弹幕list
const danmuList = useModelWrapper<Danmu[]>(props, emit, 'danmus');
const danmaku: DanmakuItem = reactive({
  channels: computed(() => props.channels || calcChannels.value),
  autoplay: computed(() => props.autoplay),
  loop: computed(() => props.loop),
  useSlot: computed(() => props.useSlot),
  debounce: computed(() => props.debounce),
  randomChannel: computed(() => props.randomChannel),
  isSuspend: computed(() => props.isSuspend),
  useSuspendSlot: computed(() => props.useSuspendSlot),
});

const danmu: DanmuItem = reactive({
  height: computed(() => danmuHeight.value),
  fontSize: computed(() => props.fontSize),
  speeds: computed(() => props.speeds),
  top: computed(() => props.top),
  right: computed(() => props.right),
});
onMounted(() => {
  init();
});
onBeforeUnmount(() => {
  clear();
});
function init() {
  initCore();
  props.isSuspend && initSuspendEvents();
  if (danmaku.autoplay) {
    play();
  }
}

function initCore() {
  containerWidth.value = container.value.offsetWidth;
  containerHeight.value = container.value.offsetHeight;
  if (containerWidth.value === 0 || containerHeight.value === 0) {
    throw new Error('获取不到容器宽高');
  }
}

function play() {
  paused.value = false;
  if (!timer) {
    timer = setInterval(() => draw(), danmaku.debounce);
  }
}
/**
 * 绘制弹幕
 */
function draw() {
  if (!paused.value && danmuList.value.length) {
    if (index.value > danmuList.value.length - 1 + insertIndex.value) {
      const screenDanmus = dmContainer.value.children.length;
      if (danmaku.loop) {
        if (index.value >= danmuList.value.length) {
          // 一轮弹幕插入完毕
          emit('list-end');
          index.value = 0;
        }
        insert();
      } else {
        if (screenDanmus < index.value) {
          clearTimer();
        }
      }
    } else {
      insert();
    }
  }
}
const insertList = ref<Danmu[]>([]);

/**
 * 插入弹幕（也暴露到外部，允许外部直接执行绘制弹幕方法）
 * @param {Object} dm 外部定义的弹幕
 */
function insert(dm?: any) {
  if (dm) insertList.value.push(dm);
  const _index = danmaku.loop ? index.value % danmuList.value.length : index.value - insertIndex.value;
  let _danmu = dm || danmuList.value[_index];

  let isOuterDm = false; // 当前取值是否是外部插入的弹幕
  if (insertList.value.length > insertIndex.value) {
    _danmu = insertList.value[insertIndex.value];
    isOuterDm = true;
  }

  let el = document.createElement(`div`);
  let sel: HTMLDivElement = document.createElement('div');
  if (danmaku.useSlot) {
    el = getSlotComponent(_danmu, _index).$el;
  } else {
    el.innerHTML = _danmu as string;
    el.setAttribute('style', props.extraStyle);
    el.style.fontSize = `${danmu.fontSize}px`;
    el.style.lineHeight = `${danmu.fontSize}px`;
  }
  el.classList.add('dm');
  // dmContainer.value.appendChild(el);
  el.style.opacity = '0';

  // 悬浮插槽
  if (danmaku.isSuspend && danmaku.useSuspendSlot) {
    sel = createSuspendVDom(_danmu, _index).childNodes[1] as HTMLDivElement;
    sel.classList.add('dm-suspend');
    sel.style.background = 'transparent';
    sel.style.display = 'none';
    if (danmaku.useSlot) {
      sel && el.childNodes[1] && el.childNodes[1].appendChild(sel);
    } else {
      sel && el.appendChild(sel);
    }
  }

  dmContainer.value && dmContainer.value.appendChild(el);

  nextTick(() => {
    if (!danmu.height) {
      danmuHeight.value = el.offsetHeight;
    }
    // 如果没有设置轨道数，则在获取到所有高度后计算出最大轨道数
    if (!danmaku.channels) {
      calcChannels.value = Math.floor(containerHeight.value / (danmu.height + danmu.top));
    }
    let channelIndex = getChannelIndex(el);
    if (channelIndex >= 0) {
      const width = el.offsetWidth;
      const height = danmu.height;
      el.classList.add('move');
      el.dataset.index = `${_index}`;
      el.style.opacity = '1';
      el.style.top = channelIndex * (height + danmu.top) + 'px';
      el.style.width = width + danmu.right + 'px';
      el.style.setProperty('--dm-scroll-width', `-${containerWidth.value + width}px`);
      el.style.left = `${containerWidth.value}px`;
      el.style.animationDuration = `${containerWidth.value / danmu.speeds}s`;
      el.addEventListener(
        'animationend',
        () => {
          if (Number(el.dataset.index) === danmuList.value.length - 1 && !danmaku.loop) {
            emit('play-end', Number(el.dataset.index));
          }
          dmContainer.value && dmContainer.value.removeChild(el);
        },
        { once: true }
      );
      index.value++;
    } else {
      dmContainer.value.removeChild(el);
    }
  });
}
/**
 * 监听移出当前元素 取消移动端悬浮
 */
function cancelSuspend() {
  document.body.addEventListener(
    'mouseout',
    (e) => {
      e.stopImmediatePropagation();
      if (suspendList.value.length) {
        suspendList.value.map((el) => {
          el.classList.remove('pause');
        });
        suspendList.value = [];
      }
    },
    { once: true }
  );
}
function getSlotComponent(_danmu: any, _index: number) {
  const DmComponent = createApp({
    render() {
      return h(
        'div',
        {
          onmouseover: (e: { target: { closest: (arg0: string) => HTMLElement; childNodes: any } }) => {
            if (!danmaku.isSuspend) return;

            // e.stopImmediatePropagation()
            const dm: HTMLElement = e.target.closest('.dm');
            if (!dm) return;
            const suspend = dm.getElementsByClassName('dm-suspend')[0] as HTMLElement; // dm.childNodes[1].childNodes[1] as HTMLElement
            if (danmaku.isSuspend && suspend) {
              suspend.style.display = 'flex';
            }
            dm.classList.add('pause');
            if (!suspendList.value.includes(dm)) {
              suspendList.value.push(dm);
              cancelSuspend();
            }
          },
          onmouseout: (e: { stopImmediatePropagation: () => void; target: { closest: (arg0: string) => HTMLElement } }) => {
            if (!danmaku.isSuspend) return;
            // e.stopImmediatePropagation()
            const dm: HTMLElement = e.target.closest('.dm');
            if (!dm) return;
            const suspend = dm.getElementsByClassName('dm-suspend')[0] as HTMLElement;
            if (danmaku.isSuspend && suspend) {
              suspend.style.display = 'none';
            }
            dm.classList.remove('pause');
            if (suspendList.value.includes(dm)) {
              const index: number = suspendList.value.indexOf(dm);
              suspendList.value.splice(index, 1);
            }
          },
        },
        [
          slots.dm &&
            slots.dm({
              danmu: _danmu,
              index: _index,
            }),
        ]
      );
    },
  });

  const ele = DmComponent.mount(document.createElement('div'));

  return ele;
}
/**
 * 创建suspend dom节点
 * @param {Danmu} danmu 当前弹幕数据
 * @param {number} index 当前弹幕下标
 * @return dom节点
 */
function createSuspendVDom(danmu: Danmu, index: number) {
  const div = ref<HTMLElement>(document.createElement('div'));
  render(
    h('div', {}, [
      slots.suspend &&
        slots.suspend({
          danmu,
          index,
        }),
    ]),
    div.value as HTMLElement
  );

  return div.value.childNodes[0];
}
function getChannelIndex(el: HTMLDivElement): number {
  let _channels = [...Array(danmaku.channels).keys()];

  if (danmaku.randomChannel) {
    _channels = _channels.sort(() => 0.5 - Math.random());
  }
  for (let i of _channels) {
    const items = danChannel.value[i];

    if (items && items.length) {
      for (let j = 0; j < items.length; j++) {
        const danRight = getDanRight(items[j]) - 10;
        // 安全距离判断
        if (danRight <= (el.offsetWidth - items[j].offsetWidth) * 0.88 || danRight <= 0) {
          break;
        }
        if (j === items.length - 1) {
          danChannel.value[i].push(el);
          el.addEventListener('animationend', () => danChannel.value[i].splice(0, 1));
          return i % danmaku.channels;
        }
      }
    } else {
      danChannel.value[i] = [el];
      el.addEventListener('animationend', () => danChannel.value[i].splice(0, 1));
      return i % danmaku.channels;
    }
  }
  return -1;
}
/**
 * 获取弹幕右侧到屏幕右侧的距离
 */
function getDanRight(el: HTMLDivElement) {
  const eleWidth = el.offsetWidth || parseInt(el.style.width);
  const eleRight = el.getBoundingClientRect().right || dmContainer.value.getBoundingClientRect().right + eleWidth;
  return dmContainer.value.getBoundingClientRect().right - eleRight;
}

function clearTimer() {
  clearInterval(timer);
  timer;
}
function initSuspendEvents() {
  let suspendDanmus: HTMLElement[] = [];
  dmContainer.value.addEventListener('mouseover', (e) => {
    let target = e.target as EventTarget & HTMLElement;

    if (!target.className.includes('dm')) {
      target = target.closest('.dm') || target;
    }

    if (!target.className.includes('dm')) return;

    if (suspendDanmus.includes(target)) return;

    const suspend = target.getElementsByClassName('dm-suspend')[0] as HTMLElement; // target.childNodes[1] as HTMLElement

    if (danmaku.isSuspend && suspend) {
      suspend.style.display = 'flex';
    }
    target.classList.add('pause');
    suspendDanmus.push(target);
    // emit('dm-over', { el: target });
  });
  dmContainer.value.addEventListener('mouseout', (e) => {
    let target = e.target as EventTarget & HTMLElement;

    if (!target.className.includes('dm')) {
      target = target.closest('.dm') || target;
    }

    if (!target.className.includes('dm')) return;
    const suspend = target.getElementsByClassName('dm-suspend')[0] as HTMLElement;
    if (danmaku.isSuspend && suspend) {
      suspend.style.display = 'none';
    }
    // emit('dm-out', { el: target });
    target.classList.remove('pause');
    // 容错处理
    suspendDanmus.forEach((item) => {
      item.classList.remove('pause');
    });
    suspendDanmus = [];
  });
}
/**
 * 清空弹幕
 */
function clear() {
  clearTimer();
  danChannel.value = {};
  dmContainer.value.innerHTML = '';
  paused.value = true;
  hidden.value = false;
  index.value = 0;
  suspendList.value = [];
}

/**
 * 重置弹幕
 */
function reset() {
  danmuHeight.value = 0;
  init();
}

/**
 * 停止弹幕
 */
function stop() {
  danChannel.value = {};
  dmContainer.value.innerHTML = '';
  paused.value = true;
  hidden.value = false;
  clear();
}

/**
 * 暂停弹幕
 */
function pause(): void {
  paused.value = true;
}
/**
 * 添加弹幕（插入到当前播放的弹幕位置）
 */
function add(danmu: Danmu): number {
  if (index.value === danmuList.value.length) {
    // 如果当前弹幕已经播放完了，那么仍然走 push
    danmuList.value.push(danmu);
    // play();
    return danmuList.value.length - 1;
  } else {
    const _index = index.value % danmuList.value.length;
    danmuList.value.splice(_index, 0, danmu);
    // play();
    return _index + 1;
  }
}

/**
 * 添加弹幕（插入到弹幕末尾）
 */
function push(danmu: Danmu): number {
  danmuList.value.push(danmu);

  return danmuList.value.length - 1;
}

/**
 * 获取播放状态
 */
function getPlayState(): boolean {
  return !paused.value;
}

/**
 * 显示弹幕
 */
function show(): void {
  hidden.value = false;
}
/**
 * 隐藏弹幕
 */
function hide(): void {
  hidden.value = true;
}

function resize() {
  initCore();
  const items = dmContainer.value.getElementsByClassName('dm');

  for (let i = 0; i < items.length; i++) {
    const el = items[i] as HTMLDivElement;

    el.style.setProperty('--dm-scroll-width', `-${containerWidth.value + el.offsetWidth}px`);
    el.style.left = `${containerWidth.value}px`;
    el.style.animationDuration = `${containerWidth.value / danmu.speeds}s`;
  }
}
defineExpose({
  add,
  push,
  insert,
  play,
  pause,
  reset,
  resize,
  show,
  hide,
  clear,
  getPlayState,
});
</script>
<style lang="scss">
.vue-danmaku {
  position: relative;
  overflow: hidden;
  .danmus {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
    &.show {
      opacity: 1;
    }
    &.paused {
      .dm.move {
        animation-play-state: paused;
      }
    }
    .dm {
      position: absolute;
      font-size: 20px;
      color: #ddd;
      white-space: pre;
      transform: translateX(0);
      transform-style: preserve-3d;
      &.move {
        will-change: transform;
        animation-name: moveLeft;
        animation-timing-function: linear;
        animation-play-state: running;
      }
      &.pause {
        animation-play-state: paused;
        z-index: 100;
      }
    }
    @keyframes moveLeft {
      from {
        transform: translateX(0);
      }
      to {
        transform: translateX(var(--dm-scroll-width));
      }
    }
    @-webkit-keyframes moveLeft {
      from {
        -webkit-transform: translateX(0);
      }
      to {
        -webkit-transform: translateX(var(--dm-scroll-width));
      }
    }
  }
}
</style>
