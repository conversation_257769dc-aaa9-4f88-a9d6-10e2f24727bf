import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
// const a = {
//   actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/94932/32/34595/128720/65004926Fe5cf254f/29b818fbf4518997.png',
//   pageBg: '',
//   actBgColor: '#ffc245',
//   shopNameColor: '#000000',
//   btnColor: '#ffffff',
//   btnBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/124488/2/38329/1752/65041689F87190ec1/dc080d3f9b88b015.png',
//   btnBorderColor: '#ffffff',
//   cutDownBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/6259/12/23227/15050/65041689F5f2b748c/1de65d625922cbe9.png',
//   cutDownColor: '#f2270c',
//   prizeBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/180338/33/38397/41718/6504168aF96857ec7/7f1ebc765294b0e6.png',
//   ruleBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/143190/23/39365/8725/64f683e0F154f640b/0a9d35184d4aac73.png',
//   btnToShop: 'https://img10.360buyimg.com/imgzone/jfs/t1/96917/39/37981/4647/65041567F5acb147d/2c1340503e3e4f39.png',
//   btnShare: 'https://img10.360buyimg.com/imgzone/jfs/t1/97801/9/45221/4681/65041567F7e06c15a/682c967082403bb3.png',
//   winnersBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/198727/40/18336/5096/619b80b9Eafb4de86/6e8012f498dd80d1.png',
//   mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/181003/34/38599/62902/650b9eadFf2e6c5d3/0d2f0e313b6401d5.png',
//   cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/181003/34/38599/62902/650b9eadFf2e6c5d3/0d2f0e313b6401d5.png',
//   h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/181003/34/38599/62902/650b9eadFf2e6c5d3/0d2f0e313b6401d5.png',
// };
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '下单有礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
