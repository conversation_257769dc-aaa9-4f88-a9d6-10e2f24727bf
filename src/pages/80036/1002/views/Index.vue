<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/228179/16/20162/769672/666bc012F28b0de1b/334cffcfdc11de0a.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div class="header-btn-all">
          <div class="header-btn" v-click-track="'hdgz'" :style="furnishStyles.ruleBtnBg.value" @click="ruleClick()"></div>
          <div class="header-btn" v-click-track="'wdjp'" :style="furnishStyles.prizeBtnBg.value" @click="prizeClick()"></div>
        </div>
      </div>
    </div>
    <div class="allCartBtnAll">
      <div class="btnClass" v-if="priceTime > dayjs().valueOf()" :style="furnishStyles.cartBtn.value" v-threshold-click="addCartAllClick"></div>
      <div class="drawBtnClass" v-else :style="furnishStyles.drawBtn.value" v-threshold-click="drawClick"></div>
    </div>
    <div class="countDownClass" v-if="baseInfo.status !== 3">
      <CountDown :endTime="endTime" :startTime="startTime" :isStart="isStart" />
    </div>
    <div class="prizeClassAll">
      <div class="prizeIntroClass" :style="furnishStyles.prizeIntroBg.value">
        <div class="cartStepContainerLine"></div>

        <div class="contentClass">
          <div class="itemClass">
            <div class="timeDateClass">{{ dayjs(addSkuStartTime).format('MM.DD') }}-{{ dayjs(addSkuEndTime).format('MM.DD') }}</div>
            <div class="stepClass"></div>
            <div class="itemTitleClass">加购商品</div>
          </div>
          <div class="itemClass">
            <div class="timeDateClass">{{ dayjs(priceTime).format('MM.DD HH:mm') }}</div>
            <div class="step2Class"></div>
            <div class="itemTitleClass">抽奖</div>
          </div>
          <div class="itemClass">
            <div class="timeDateClass">获奖者</div>
            <div class="step3Class"></div>
            <div class="itemTitleClass">领取丰厚大奖</div>
          </div>
        </div>
      </div>
      <div class="joinClassAll">
        <div class="logoClass" :style="furnishStyles.joinLogo.value"></div>
        <div class="joinClass" :style="furnishStyles.joinColor.value">
          已有<span class="joinNumClass" :style="furnishStyles.joinNumColor.value">{{ joinNum }}</span
          >人参与活动
        </div>
      </div>
    </div>
    <div class="goodsClassAll">
      <div class="titleClass" :style="furnishStyles.skuTitleBg.value"></div>
      <div class="goodListClass">
        <div class="goodItemClass" v-for="(item, index) in orderSkuList" :key="index">
          <div class="unLockClass" v-if="index >= unlockSkuNumber">
            <div class="unlockImageAll1">
              <div class="unlockImageAll">
                <div class="unlockImage"></div>
              </div>
            </div>
            <div class="shareClassAll">
              <div class="shareClass" v-threshold-click="shareFriendsClick"></div>
            </div>
          </div>
          <div @click.stop="gotoSkuPage(item.skuId)">
            <div class="imgClass">
              <img :src="item.skuMainPicture" alt="" />
            </div>
            <div class="goodTitleClass">{{ item.skuName }}</div>
            <div class="detailClass">
              <div class="priceClass">{{ item.jdPrice }}</div>
              <div class="btnClass" v-if="item.isAddCart === 0" v-threshold-click="() => addCartClick(item, index)">加购</div>
              <div class="btnGrayClass" v-else-if="item.isAddCart === 1">已加购</div>
            </div>
          </div>
        </div>
      </div>
      <div class="load-more" @click="handleLoadMore" v-if="orderSkuList.length">加载更多</div>
    </div>
    <div class="goToShop" @click="showGoShop = true"></div>

    <div class="bottom-div">我也是有底线的哦~</div>
  </div>
  <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-model:show="showRule" position="bottom">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
    <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="showAward">
    <AwardPopup @openShowGoShop="showGoShop = true" :prize="award" @close="showAward = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></AwardPopup>
  </VanPopup>

  <!-- 保存地址弹窗 -->
  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" :echoData="echoData" @close="showSaveAddress = false"></SaveAddress>
  </VanPopup>
  <!-- 展示卡密 -->
  <VanPopup teleport="body" v-model:show="copyCardPopup">
    <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
  </VanPopup>
  <!-- 领取京元宝权益 -->
  <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom">
    <SavePhone v-if="savePhonePopup" :userPrizeId="activityPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"></SavePhone>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="showHelpDrawPopup" position="center">
    <HelpDraw v-if="showHelpDrawPopup" :helpResult="helpResult" :helpFailCause="helpFailCause" @close="showHelpDrawPopup = false"></HelpDraw>
  </VanPopup>
  <!-- 进店逛逛 -->
  <VanPopup teleport="body" v-model:show="showGoShop" position="bottom" z-index="10000">
    <GoShopPop v-if="showGoShop" @close="showGoShop = false"></GoShopPop>
  </VanPopup>
</template>

<script setup lang="ts">
import { inject, onMounted, reactive, ref } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { CardType, FormType } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import SavePhone from '../components/SavePhone.vue';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import dayjs from 'dayjs';
import CountDown from '../components/CountDown.vue';
import HelpDraw from '../components/helpDraw.vue';
import { gotoSkuPage } from '@/utils/platforms/jump';
import constant from '@/utils/constant';
import { callShare } from '@/utils/platforms/share';
import GoShopPop from '../components/GoShopPop.vue';

import { Handler } from '@/utils/handle';

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const pathParams = inject('pathParams') as any;

const shopName = ref(baseInfo.shopName);
const unlockSkuNumber = ref(0);
const addSkuStartTime = ref(0);
const addSkuEndTime = ref(0);
const priceTime = ref(0);
const endTime = ref(0);
const startTime = ref(0);
const isStart = ref(true);
const joinNum = ref(0);
const hasChance = ref(0); // 是否有抽奖机会

// 助力结果
const showHelpDrawPopup = ref(false);
const helpFailCause = ref('');
const helpResult = ref(false);
const showRule = ref(false);
const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error: any) {
    console.error();
  }
};

const showMyPrize = ref(false);

type Sku = {
  skuName: string;
  skuMainPicture: string;
  skuId: number;
  jdPrice: string;
  isAddCart: number;
};
const showGoShop = ref(false);
const pageNum = ref(1);
const orderSkuList = ref<Sku[]>([]);
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});
const echoData: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});
const handler = Handler.getInstance();
onMounted(() => {
  handler.on('onGoShopOpen', () => {
    showGoShop.value = true;
  });
});
// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  showAward.value = false;
  showSaveAddress.value = true;
};

const ruleClick = () => {
  showRulePopup();
};
const prizeClick = () => {
  showMyPrize.value = true;
};
// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showAward.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  showAward.value = false;
  showMyPrize.value = false;
  savePhonePopup.value = true;
};
// 邀请好友助力
const shareFriendsClick = () => {
  if (addSkuStartTime.value > dayjs().valueOf() || addSkuEndTime.value < dayjs().valueOf()) {
    showToast('未在加购时间内，无法邀请');
    return;
  }
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
const myLucky = ref();
// 抽奖接口
const drawClick = async () => {
  if (hasChance.value <= 0) {
    showToast('您加购的商品数量不满足抽奖所需的数量');
    return;
  }
  if (hasChance.value === 2) {
    // 已经开奖
    try {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
      const res = await httpRequest.post('/80036/userPrizes');
      closeToast();
      if (res.data && res.data.length > 0) {
        if (res.data[0].prizeType === 3) {
          Object.keys(echoData).forEach((key) => {
            echoData[key] = res.data[0][key];
          });
        }
        award.value = {
          prizeType: res.data[0].prizeType,
          prizeName: res.data[0].prizeName,
          showImg: res.data[0].prizeImg,
          result: res.data[0].prizeContent ? JSON.parse(res.data[0].prizeContent) : '',
          activityPrizeId: res.data[0].activityPrizeId ?? '',
          userPrizeId: res.data[0].userPrizeId,
        };
        showAward.value = true;
      } else {
        award.value = {
          prizeType: 0,
          prizeName: '谢谢参与',
          showImg: '',
          result: '',
          activityPrizeId: '',
          userPrizeId: '',
        };
        showAward.value = true;
      }
    } catch (error: any) {
      award.value = {
        prizeType: 0,
        prizeName: '谢谢参与',
        showImg: '',
        result: '',
        activityPrizeId: '',
        userPrizeId: '',
      };
      closeToast();
      showAward.value = true;
    }
  } else if (hasChance.value === 1) {
    // 有开奖机会并且未开过将
    try {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
      lzReportClick('kscj');
      const res = await httpRequest.post('/80036/lotteryDraw');
      hasChance.value = 2;
      closeToast();
      if (res.data.prizeType) {
        award.value = {
          prizeType: res.data.prizeType,
          prizeName: res.data.prizeName,
          showImg: res.data.prizeImg,
          result: res.data.result ?? '',
          activityPrizeId: res.data.activityPrizeId ?? '',
          userPrizeId: res.data.userPrizeId,
        };
        showAward.value = true;
      } else {
        award.value = {
          prizeType: 0,
          prizeName: '谢谢参与',
          showImg: '',
          result: '',
          activityPrizeId: '',
          userPrizeId: '',
        };
        showAward.value = true;
      }
    } catch (error: any) {
      award.value = {
        prizeType: 0,
        prizeName: '谢谢参与',
        showImg: '',
        result: '',
        activityPrizeId: '',
        userPrizeId: '',
      };
      hasChance.value = 2;
      closeToast();
      showAward.value = true;
    }
  }
};

// 主接口
const getActivityData = async () => {
  try {
    const { data } = await httpRequest.post('/80036/activity', {
      shareUserId: pathParams.shareId,
    });
    addSkuStartTime.value = data.addSkuStartTime;
    addSkuEndTime.value = data.addSkuEndTime;
    priceTime.value = data.priceTime;
    joinNum.value = data.joinNum;
    unlockSkuNumber.value = data.unlockSkuNumber;
    hasChance.value = data.hasChance;
    // showHelpDrawPopup
    if (data.shareFlag) {
      // 是否点分享链接进入活动
      showHelpDrawPopup.value = true;
      helpFailCause.value = data.cause;
      helpResult.value = data.isSuccess;
      if (helpResult.value) {
        lzReportClick('help');
      }
    } else {
      showHelpDrawPopup.value = false;
    }
    console.log(data, 'datadata');
  } catch (error: any) {
    console.error(error);
  }
};
// 获取加购商品
const getExposureSkuPage = async () => {
  try {
    const { data } = await httpRequest.post('/80036/getExposureSkuPage', {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 20,
    });
    orderSkuList.value = data.records;
    // console.log(data, 'datadata');
  } catch (error: any) {
    console.error(error);
  }
};
const handleLoadMore = async () => {
  pageNum.value++;
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/80036/getExposureSkuPage', {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 20,
    });
    closeToast();
    if (data.records.length === 0) {
      showToast({
        message: '没有更多数据了',
        duration: 2000,
      });
      return;
    }
    orderSkuList.value.push(...data.records);
    // console.log(data, 'datadata');
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};
// 一键加购
const addCartAllClick = async () => {
  if (addSkuStartTime.value > dayjs().valueOf() || addSkuEndTime.value < dayjs().valueOf()) {
    showToast('未在加购时间内，无法加购商品');
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/80036/addSku', {
      skuId: '999',
    });
    if (res.code === 200) {
      showToast('加购成功');
      pathParams.shareId = '';
      pageNum.value = 1;
      getExposureSkuPage();
      await getActivityData();
    } else {
      showToast('加购失败');
    }
  } catch (error: any) {
    console.error(error);
    showToast(error);
  }
};
// 逐件加购商品
const addCartClick = async (itemData: Sku, index: number) => {
  if (addSkuStartTime.value > dayjs().valueOf() || addSkuEndTime.value < dayjs().valueOf()) {
    showToast('未在加购时间内，无法加购商品');
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/80036/addSku', {
      skuId: itemData.skuId,
    });
    if (res.code === 200) {
      showToast('加购成功');
      pathParams.shareId = '';
      await getActivityData();
      orderSkuList.value[index].isAddCart = 1;
    } else {
      showToast('加购失败');
    }
  } catch (error: any) {
    console.error(error);
    showToast(error);
  }
};

// 活动倒计时
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  isStart.value = baseInfo.status === 2;
};

const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getActivityData(), getExposureSkuPage()]);
    closeToast();
  } catch (error: any) {
    closeToast();
  }
  if (baseInfo.status === 1) {
    const time = baseInfo.startTime - dayjs().valueOf();
    setTimeout(() => {
      window.location.reload();
    }, time);
  }
};
init();
</script>

<style>
::-webkit-scrollbar {
  width: 0 !important;
}
</style>
<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  .shop-name-text {
    font-size: 0.24rem;
    position: absolute;
    top: 0.26rem;
    left: 0.3rem;
  }
  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0;
    display: flex;
    justify-content: space-between;
  }
  .shop-name {
    font-size: 0.24rem;
    width: 100%;
    margin-top: 0.3rem;
    margin-left: 0.3rem;
  }
  .header-btn-all {
    position: absolute;
    top: 0.3rem;
    right: 0.3rem;
    z-index: 10;
    .header-btn {
      width: 1.22rem;
      background-size: 100%;
      background-repeat: no-repeat;
      height: 0.53rem;
      margin-bottom: 0.1rem;
      font-size: 0.2rem;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }
}
.countDownClass {
  width: 100%;
  display: flex;
  justify-content: center;
}
.allCartBtnAll {
  display: block;
  margin-top: -1rem;
  position: relative;
  z-index: 3;
  .btnClass {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/220340/40/6804/62076/61aed0d0Ef010cc65/4d146c5c4c17f7f6.png');
    background-repeat: no-repeat;
    background-size: 100%;
    width: 5.17rem;
    height: 0.97rem;
    margin: 0.3rem auto;
    display: block;
  }
  .drawBtnClass {
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/99279/26/30469/87280/65093b3cF4ae4a390/11335bccb462408a.png');
    background-repeat: no-repeat;
    background-size: 100%;
    width: 5.17rem;
    height: 0.97rem;
    margin: 0.3rem auto;
    display: block;
  }
}
.prizeClassAll {
  display: flex;
  align-items: center;
  width: 100%;
  flex-direction: column;
  margin: 0.3rem auto 0;
  .prizeIntroClass {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/216619/5/6765/149521/61ae040bE44547815/a89c4756f0049005.png');
    background-repeat: no-repeat;
    background-size: 100%;
    width: 6.91rem;
    height: 4.26rem;
    box-sizing: border-box;
    position: relative;
    padding-top: 2.34rem;
    .cartStepContainerLine {
      width: 5.2rem;
      height: 0.08rem;
      background: rgb(255, 197, 113);
      border-radius: 0.04rem;
      position: absolute;
      left: 50%;
      transform: translate(-50%, 0.45rem);
      z-index: 0;
    }
    .contentClass {
      width: 5.2rem;
      margin: 0 auto;
      border-radius: 0.04rem;
      display: flex;
      justify-content: space-between;
      .itemClass {
        display: flex;
        flex-direction: column;
        justify-content: center;
        .timeDateClass {
          font-size: 0.24rem;
          color: #262626;
          line-height: 0.24rem;
          text-align: center;
        }
        .stepClass {
          background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/138265/27/22547/2779/61ae06e0E11257235/49ae1435707eb4fb.png');
          background-repeat: no-repeat;
          background-size: 100%;
          width: 0.52rem;
          height: 0.52rem;
          margin: 0 auto;
          position: relative;
        }
        .step2Class {
          background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/205456/15/17571/2934/61ae06e0Ea1bc543c/adbb23a655da3d73.png');
          background-repeat: no-repeat;
          background-size: 100%;
          width: 0.52rem;
          height: 0.52rem;
          margin: 0 auto;
          position: relative;
        }
        .step3Class {
          background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/212901/20/6908/2980/61ae06e0E4ab60495/1124268d88c487fa.png');
          background-repeat: no-repeat;
          background-size: 100%;
          width: 0.52rem;
          height: 0.52rem;
          margin: 0 auto;
          position: relative;
        }
        .itemTitleClass {
          font-size: 0.24rem;
          color: #f2270c;
          line-height: 0.24rem;
          text-align: center;
        }
      }
    }
  }
  .joinClassAll {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 0.4rem;
    background: linear-gradient(45deg, rgb(254, 224, 226), transparent);
    min-width: 3.75rem;
    max-width: 5rem;
    height: 0.4rem;
    margin-left: 50%;
    transform: translate(-50%);
    position: relative;
    .logoClass {
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/174684/1/37277/9810/65002eb6Ffb442c78/93aecf7f853e731e.png);
      background-size: 100%;
      background-repeat: no-repeat;
      width: 0.81rem;
      height: 0.77rem;
      margin-right: 0.1rem;
      position: absolute;
      left: -0.2rem;
      top: -0.3rem;
    }
    .joinClass {
      font-size: 0.24rem;
      color: #ffffff;
      .joinNumClass {
        font-size: 0.24rem;
        color: #fff100;
      }
    }
  }
}
.goodsClassAll {
  width: 6.9rem;
  background: linear-gradient(0deg, rgb(255, 220, 115) 0%, rgb(254, 245, 207) 100%);
  border-radius: 0.2rem;
  margin-left: calc(50% - 3.45rem);
  padding-top: 0.38rem;
  padding-bottom: 0.3rem;
  margin-top: 0.38rem;
  .titleClass {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/198727/40/18336/5096/619b80b9Eafb4de86/6e8012f498dd80d1.png');
    background-repeat: no-repeat;
    background-size: 100%;
    width: 2.82rem;
    height: 0.4rem;
    margin-left: 50%;
    transform: translate(-50%);
    margin-bottom: 0.3rem;
  }
  .goodListClass {
    margin: 0 auto;
    display: flex;
    place-content: flex-start space-between;
    flex-wrap: wrap;
    padding: 0 0.3rem;
    .goodItemClass {
      width: calc(50% - 0.1rem);
      margin-bottom: 0.2rem;
      background: #ffffff;
      border-radius: 0.2rem;
      overflow: hidden;
      padding-bottom: 0.2rem;
      position: relative;
      .imgClass {
        display: block;
        width: 100%;
        img {
          width: 100%;
        }
      }
      .goodTitleClass {
        overflow: hidden;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        font-size: 0.3rem;
        color: #262626;
        padding: 0 0.2rem;
        margin: 0.2rem 0 0.14rem;
        box-sizing: border-box;
      }
      .detailClass {
        width: 3rem;
        display: flex;
        margin: 0 auto;
        align-items: center;
        .priceClass {
          width: 1.95rem;
          font-size: 0.3rem;
          color: #ff275a;
          text-align: left;
          padding-left: 0.2rem;
          box-sizing: border-box;
        }
        .btnClass {
          width: 0.85rem;
          height: 0.39rem;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/221595/11/36213/1576/65002eb5F16ad4345/e1b3e7049c51ad5b.png);
          font-size: 0;
        }
        .btnGrayClass {
          width: 0.85rem;
          height: 0.39rem;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/119846/24/35146/2278/65012177Fc07037b1/9d48b8648abe0288.png);
          font-size: 0;
        }
      }
      .unLockClass {
        position: absolute;
        width: 100%;
        height: 100%;
        right: 0;
        bottom: 0;
        z-index: 3;
        background: rgba(0, 0, 0, 0.4);
        .unlockImageAll1 {
          width: 3.4rem;
          height: 3.4rem;
          display: flex;
          justify-content: center;
          align-items: center;
          .unlockImageAll {
            width: 1.6rem;
            height: 1.6rem;
            background: #000000;
            opacity: 0.4;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            .unlockImage {
              background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/137944/8/22945/767/61aec6edE5153c4cd/1b9d8267e72a3169.png');
              background-repeat: no-repeat;
              background-size: 100%;
              width: 0.442rem;
              height: 0.53rem;
            }
          }
        }
        .shareClassAll {
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 0.5rem;
          .shareClass {
            background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/173509/10/21564/13130/61aecf05E901527c7/706f77f1dde4c468.png');
            background-repeat: no-repeat;
            background-size: 100%;
            width: 2.1rem;
            height: 0.58rem;
          }
        }
      }
    }
  }
  .load-more {
    width: 3rem;
    height: 0.6rem;
    line-height: 0.6rem;
    text-align: center;
    background: #fb6500;
    border-radius: 0.2rem;
    color: white;
    font-weight: 600;
    margin: 0 auto;
    font-size: 0.28rem;
  }
}
.goToShop {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  z-index: 10;
  width: 7.5rem;
  height: 0.96rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/221887/35/36771/3853/650124e9Fdfd5e8fb/4b919df16ba70ce6.png);
  background-size: 100%;
}
.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
</style>
