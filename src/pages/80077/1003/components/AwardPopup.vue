<template>
  <div class="bk" v-if="prize.prizeType !== 0">
    <div class="close" @click="close"></div>
    <img :src="prize.prizeImg" alt="" class="prize-img" />
    <div class="content">
      <p class="prize-name">{{ prize.prizeName }}</p>
      <div>
        <div class="p3" v-if="prize.prizeType === 3">您已经完成活动报名，在完成订单付款，并最终确认收货后发货。请在报名后1小时之内及时填写邮寄地址，以免礼品发放失败！未完成付款或未确认收货的将无法获得礼品</div>
        <div class="p3" v-else-if="prize.prizeType === 12">您已经完成活动报名，在完成订单付款，并最终确认收货后发放。请在报名后1小时之内及时填写信息，以免礼品发放失败！未完成付款或未确认收货的将无法获得礼品</div>
        <div class="p3" v-else-if="prize.prizeType === 7">您已经完成活动报名，在完成订单付款，并最终确认收货后发放（前往我的奖品进行兑换）。未完成付款或未确认收货的将无法获得礼品</div>
        <div class="p3" v-else>您已经完成活动报名，虚拟礼品将在您完成订单付款，并最终确认收货后20至30分钟内自动发放，未完成付款或未确认收货的将无法获得礼品</div>
      </div>
      <div class="btn-list">
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/243273/4/261/5601/65856728F372692e2/8c123cf2a060c396.png" alt="" @click="shareAct" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/227922/31/9587/14921/6585687bF941cf4fe/075adffbfa8abe99.png" alt="" v-if="prize.prizeType === 3" @click="saveAddress" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/232224/37/9830/15570/6585687bF21c95544/c8a65addcaf2f71b.png" alt="" v-else-if="prize.prizeType === 12" @click="savePhone" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/232805/3/9580/15482/658564aaF8fc07085/898bff9bd7475232.png" alt="" v-else @click="close" />
      </div>
    </div>
  </div>
  <div class="thanks-join" v-else>
    <div class="close" @click="close"></div>
    <div class="btn" @click="gotoShopPage">进店逛逛</div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import { callShare } from '@/utils/platforms/share';
import { PropType, inject } from 'vue';

const baseInfo = inject('baseInfo') as BaseInfo;

interface PrizeType {
  prizeType: number;
  prizeName: string;
  prizeImg: string;
  result: any;
  activityPrizeId: string;
  userPrizeId: string;
  prizeContent: string;
  userReceiveRecordId: string;
}
const props = defineProps({
  prize: {
    type: Object as PropType<PrizeType>,
    required: true,
  },
});

const emits = defineEmits(['close', 'saveAddress', 'showCardNum', 'savePhone', 'openShowGoShop']);

const close = () => {
  emits('close');
};
const gotoShopPage = () => {
  emits('openShowGoShop');
};

const saveAddress = () => {
  emits('saveAddress', props.prize.userReceiveRecordId, props.prize.activityPrizeId);
};

const showCardNum = () => {
  emits('showCardNum', { ...props.prize.result, prizeImg: props.prize.prizeImg, prizeName: props.prize.prizeName });
};

const savePhone = () => {
  const prizeContent = JSON.parse(props.prize.prizeContent);
  emits('savePhone', props.prize.userPrizeId, prizeContent.result.planDesc);
};

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
</script>

<style scoped lang="scss">
.bk {
  height: 8.2rem;
  width: 6.9rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/231882/18/9726/37347/65855dafF08068cfe/38ef284beea6cdd5.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 1.2rem;
  .prize-img {
    height: 2rem;
    width: 2rem;
    //border-radius: 50%;
    margin: 1.8rem auto 0 auto;
  }
  .content {
    padding: 0 0.3rem;
    .p1 {
      display: block;
      color: #262626;
      font-size: 0.24rem;
      font-weight: 500;
    }
    .prize-name {
      font-size: 0.4rem;
      font-weight: bold;
      margin: 0.2rem 0 0;
      text-align: center;
      color: #000;
    }
    .p3 {
      font-size: 0.22rem;
      color: #000;
      text-align: center;
      height: 1.2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      padding:0 0.2rem;
      span {
        color: #f2270c;
      }
    }
    .btn-list {
      display: flex;
      justify-content: space-between;
      padding: 0 0.5rem;
      margin-top: 0.2rem;
      .btn {
        width: 2.1rem;
      }
      .btn-left {
        background: linear-gradient(to right, #f2270c, #ff6320);
      }
      .btn-right {
        background: #ff9900;
      }
    }
  }
}
.thanks-join {
  width: 6rem;
  height: 7.89rem;
  position: relative;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/98130/39/37445/93706/65019837Fca4f3e8e/02538ed5f33a63ec.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 5.7rem;
  .btn {
    display: block;
    margin: 0 auto;
    width: 2.6rem;
    height: 0.76rem;
  }
}
.close {
  height: 0.25rem;
  width: 0.25rem;
  position: absolute;
  top: 0.8rem;
  left: 6.2rem;
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/244842/29/304/382/65854d6bF3a09cad9/177a5b81b3914e16.png");
  background-repeat: no-repeat;
  background-size: 100%;
}
</style>
