import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const a = {
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/157671/10/35438/100590/6507f197F7c4c9af2/a4f45beacbb71ba2.png',
  pageBg: '',
  actBgColor: '#f54e46',
  shopNameColor: '#000000',
  btnColor: '#ffffff',
  btnBg: '#ffffff',
  btnBorderColor: '#df4226',
  ruleBg: '//img10.360buyimg.com/imgzone/jfs/t1/238203/8/12345/1752/65b324e5F0b6b8bb0/453ce8349888d2f7.png',
  myPrizeBg: '',
  myOrderBg: '',
  cutDownBg: '//img10.360buyimg.com/imgzone/jfs/t1/241516/19/3048/14278/65b31c2dFfd47d9c4/c8dd26ead15a0c3e.png',
  cutDownColor: '#ffffba',
  cutDownNumBg: '#8b4004',
  cutDownNumColor: '#ffffba',
  prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/236270/10/12377/7158/65b31c26F15b49008/2060a620ea40d9bc.png',
  prizeNameColor: '#ffffba',
  getPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/194888/18/36346/17179/65081266F780d9b3f/77b690c7a9e275da.png',
  showSkuBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/160186/34/41529/11408/650807fdFb5b7f959/c7209966aa477011.png',
  winnersBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/182570/35/38327/95442/650807f1Fb3010c57/87a3d655cc7860a3.png',
  btnToShop: 'https://img10.360buyimg.com/imgzone/jfs/t1/176148/21/41080/4440/650807f1Feeaaa019/5fab88c51c978e03.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/220455/25/36389/31876/650c13aaF671e9216/73b5b58ebe902b0b.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/220455/25/36389/31876/650c13aaF671e9216/73b5b58ebe902b0b.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/220455/25/36389/31876/650c13aaF671e9216/73b5b58ebe902b0b.png',
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
