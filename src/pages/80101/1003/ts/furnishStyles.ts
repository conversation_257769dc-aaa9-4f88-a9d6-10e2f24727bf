import { computed, reactive } from 'vue';
import { prizePlateArray } from '@/utils/prizePlateArray';

export const prizeInfo = reactive([
  {
    id: '',
    index: 1,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 2,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 3,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 4,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 5,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 6,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 7,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 8,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
]);

export const furnish = reactive({
  // 活动主图
  actBg: '',
  // 页面背景图
  pageBg: '',
  // 页面背景颜色
  actBgColor: '',
  // 文字颜色
  shopNameColor: '',
  // 按钮
  btnColor: '',
  btnBg: '',
  btnBorderColor: '',
  wheelType: '',
  // 奖盘背景图
  wheelBg: '',
  wheelPanel: '',
  wheelBtn: '',
  // 奖盘按钮背景图
  drawBtn: '',
  // 奖盘文案颜色
  wheelTextColor: '',
  // 抽奖次数文案颜色
  drawsNum: '',
  // 获奖名单背景图
  winnersBg: '',
  disableShopName: 0,
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const headerBtn = computed(() => ({
  color: furnish.btnColor ?? '',
  backgroundImage: `url(${furnish.btnBg})`,
}));

const drawsNum = computed(() => ({
  color: furnish.drawsNum ?? '',
}));

const winnersBg = computed(() => ({
  backgroundImage: furnish.winnersBg ? `url(${furnish.winnersBg})` : '',
}));

const wheelTpl = {
  hasImg: {
    fonts: [{ text: '', top: '30%', fontColor: 'rgb(131, 75, 235)', fontSize: '0.2rem' }],
    imgs: [
      {
        src: '',
        width: '35%',
        top: '55%',
        borderRadius: '50%',
      },
    ],
  },
  noImg: { fonts: [{ text: '', top: '30%', fontColor: 'rgb(131, 75, 235)', fontSize: '0.2rem' }] },
};

const params = computed(() => {
  const prizePlate = prizePlateArray.find((item: any) => item.splicingBg === furnish.wheelBg);
  const block = {
    padding: '12px',
    imgs: [
      {
        src: furnish.wheelType !== '2' ? prizePlate?.turnUrl : furnish.wheelPanel,
        height: '100%',
        rotate: true,
      },
    ],
  };
  const button = {
    radius: '45%',
    imgs: [
      {
        src: furnish.wheelType !== '2' ? prizePlate?.turnBtn : furnish.wheelBtn,
        width: '60%',
        top: '-80%',
      },
    ],
  };
  const prizes = new Array(8).fill(null);
  for (let i = 0; i < prizes.length; i++) {
    const prize = prizeInfo[i];
    if (prize.prizeImg) {
      prizes[i] = JSON.parse(JSON.stringify(wheelTpl.hasImg));
      prizes[i].imgs[0].src = prize.prizeImg;
    } else {
      prizes[i] = JSON.parse(JSON.stringify(wheelTpl.noImg));
    }
    prizes[i].fonts[0].text = prize.prizeName;
    prizes[i].fonts[0].fontColor = furnish.wheelTextColor ?? '#834beb';
  }
  return {
    blocks: [block],
    buttons: [button],
    prizes,
    defaultConfig: {
      accelerationTime: 2000,
      decelerationTime: 3000,
      speed: 15,
    },
  };
});

export default {
  pageBg,
  shopNameColor,
  headerBtn,
  drawsNum,
  winnersBg,
  params,
};
