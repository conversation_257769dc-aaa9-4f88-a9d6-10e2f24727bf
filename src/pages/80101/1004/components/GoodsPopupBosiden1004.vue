<template>
  <div class="rule-bk">
    <div class="title">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/231616/37/9833/1864/658574f0F2d4c424d/fb7d1d5e85db4e8e.png" alt="" class="text" />
      <div class="close" @click="emits('close')"></div>
    </div>
    <div class="h-[40vh] px-4 leading-5 overflow-y-scroll text-[#33333] whitespace-pre-wrap text-xs">
      <div class="text-gray-400 text-sm flex justify-center pb-4 sku-list-box" v-if="skuList.length || data.length">
        <div class="grid grid-cols-2 gap-2">
          <div v-for="(item, index) in skuList.length ? skuList : data" class="bg-white py-2 px-3.5" :key="index" @click="gotoSkuPage(item.skuId)">
            <div class="flex justify-center">
              <img class="w-32 h-32" :src="item.skuMainPicture" alt="" />
            </div>
            <div class="text-xs mt-5 lz-multi-ellipsis--l2" v-text="item.skuName"></div>
            <div class="text-red-500 text-xs mt-3">¥ <span v-text="item.jdPrice"></span></div>
          </div>
          <!-- <div class="w-[6.34rem] text-gray-400 text-xs text-center my-1" v-if="skuList.length > 4 || data.length > 4">—— 没有更多了 ——</div> -->
        </div>
        <div class="load-more" @click="handleLoadMore">加载更多</div>
      </div>
      <div v-else class="no-data">
        活动商品为本店全部商品
        <div class="btn" @click="gotoShopPage">进店逛逛</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CommonDrawer from '@/components/CommonDrawer/index.vue';
import { gotoSkuPage } from '@/utils/platforms/jump';
import { ref, defineEmits, defineProps, inject, watch } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { isPreview } from '@/utils';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps(['data']);
const emits = defineEmits(['close', 'openShowGoShop']);
const skuList = ref<any[]>([]);
const pageNum = ref(1);
// 获取曝光商品
const getSkuList = async () => {
  const params = {
    pageNum: pageNum.value,
    pageSize: 20,
    type: 1,
  };
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    const { data } = await httpRequest.post('/80101/getExposureSkuPage', params);
    skuList.value = data.records as any[];
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};
const gotoShopPage = () => {
  if (isPreview) {
    showToast('活动预览，仅供查看');
    return;
  }
  emits('openShowGoShop');
};
if (!isPreview) {
  getSkuList();
} else {
  watch(props.data, () => {
    skuList.value = props.data;
  });
}
// 加载更多
const handleLoadMore = async () => {
  if (isPreview) {
    showToast('活动预览，仅供查看');
    return;
  }
  pageNum.value++;
  const params = {
    pageNum: pageNum.value,
    pageSize: 20,
    type: 1,
  };
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 2000,
    overlay: true,
  });
  try {
    const { data } = await httpRequest.post('/80101/getExposureSkuPage', params);
    if (data.records.length === 0) {
      showToast({
        message: '没有更多数据了',
        duration: 2000,
      });
      return;
    }
    skuList.value.push(...data.records);
    closeToast();
  } catch (error: any) {
    console.error(error);
    closeToast();
  }
};
</script>

<style scoped lang="scss">
.rule-bk {
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/222600/40/35224/219814/65016dfeF9234602d/d99de4f864849a24.png) no-repeat;
  background-size: 100%;
  width: 100vw;
  min-height: 7rem;
  .title {
    position: relative;
    height: 1rem;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0 0.33rem 0.1rem 0.33rem;
    .text {
      height: 0.62rem;
    }
  }

  .close {
    width: 0.55rem;
    height: 0.55rem;
  }
}
.no-data {
  text-align: center;
  padding-top: 3rem;
  font-size: 0.3rem;
  color: #fff;
  .btn {
    width: 2.4rem;
    height: 0.9rem;
    line-height: 0.9rem;
    text-align: center;
    color: white;
    font-size: 0.3rem;
    border-radius: 0.1rem;
    background-color: #ff9900;
    margin: 0.3rem auto;
  }
}
.sku-list-box {
  position: relative;
  padding-bottom: 1rem;
  .load-more {
    width: 2rem;
    height: 0.4rem;
    line-height: 0.4rem;
    text-align: center;
    background: linear-gradient(to right, #ff1f53, #ffd102);
    border-radius: 0.2rem;
    color: white;
    position: absolute;
    bottom: 0.24rem;
    left: 50%;
    transform: translateX(-50%);
    font-weight: 600;
  }
}
</style>
