import { computed, reactive } from 'vue';
import { prizePlateArray } from '@/utils/prizePlateArray';

export const prizeInfo = reactive([
  {
    id: '',
    index: 1,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 2,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 3,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 4,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 5,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 6,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 7,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 8,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
]);

export const furnish = reactive({
  // 活动主图
  actBg: '',
  // 页面背景图
  pageBg: '',
  // 页面背景颜色
  actBgColor: '',
  // 文字颜色
  shopNameColor: '',
  // 按钮
  btnColor: '',
  btnBg: '',
  btnBorderColor: '',
  // 奖盘背景图
  wheelBg: '',
  // 奖盘按钮背景图
  drawBtn: '',
  // 奖盘文案颜色
  wheelTextColor: '',
  // 抽奖次数文案颜色
  drawsNum: '',
  // 获奖名单背景图
  winnersBg: '',
  drawsNumBg: '',
  disableShopName: 0,
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const headerBtn = computed(() => ({
  color: furnish.btnColor ?? '',
  backgroundImage: `url(${furnish.btnBg})`,
}));

const drawsNum = computed(() => ({
  color: furnish.drawsNum ?? '',
  backgroundImage: furnish.drawsNumBg ? `url(${furnish.drawsNumBg})` : 'url(//img10.360buyimg.com/imgzone/jfs/t1/238619/14/1222/6322/658a33f6Fb2734429/a7ed3989ea59702f.png)',
}));

const winnersBg = computed(() => ({
  backgroundImage: furnish.winnersBg ? `url(${furnish.winnersBg})` : '',
}));

const wheelTpl = {
  hasImg: {
    fonts: [{ text: '', top: '30%', fontColor: 'rgb(131, 75, 235)', fontSize: '0.14rem' }],
    imgs: [
      {
        src: '',
        width: '35%',
        top: '65%',
        borderRadius: '50%',
      },
    ],
  },
  noImg: { fonts: [{ text: '', top: '30%', fontColor: 'rgb(131, 75, 235)', fontSize: '0.2rem' }] },
};

const params = computed(() => {
  const prizePlate = prizePlateArray.find((item: any) => item.splicingBg === furnish.wheelBg);
  const block = {
    padding: '12px',
    imgs: [
      {
        src: prizePlate?.turnUrl ?? '//img10.360buyimg.com/imgzone/jfs/t1/222464/40/33081/195825/6568012aF3bb2b559/4e9b8787235fb966.png',
        height: '100%',
        rotate: true,
      },
    ],
  };
  const button = {
    radius: '45%',
    imgs: [
      {
        src: prizePlate?.turnBtn ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/199775/27/36153/16189/64801816F27b9ee75/fb295c8dad40149d.png',
        width: '60%',
        top: '-80%',
      },
    ],
  };
  const prizes = new Array(8).fill(null);
  for (let i = 0; i < prizes.length; i++) {
    const prize = prizeInfo[i];
    if (prize.prizeImg) {
      prizes[i] = JSON.parse(JSON.stringify(wheelTpl.hasImg));
      prizes[i].imgs[0].src = prize.prizeImg;
    } else {
      prizes[i] = JSON.parse(JSON.stringify(wheelTpl.noImg));
    }
    prizes[i].fonts[0].text = prize.prizeName;
    prizes[i].fonts[0].fontColor = furnish.wheelTextColor ?? '#834beb';
  }
  return {
    blocks: [block],
    buttons: [button],
    prizes,
    defaultConfig: {
      accelerationTime: 2000,
      decelerationTime: 3000,
      speed: 15,
    },
  };
});

export default {
  pageBg,
  shopNameColor,
  headerBtn,
  drawsNum,
  winnersBg,
  params,
};
