<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img v-if="actData.num === 1" :src="furnish.actBg" alt="" class="kv-img" />
      <img v-if="actData.num === 2" :src="furnish.actBg2" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value"></div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="rulePopup = true"  v-click-track="'hdgz'">活动规则</div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="strategyPopup = true" v-click-track="'hdgl'">活动攻略</div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="recordPopup = true" v-click-track="'sqjl'">锁权记录</div>
        </div>
      </div>
    </div>
    <div class="blank">
      <div class="exchange-area">
        <img :src="actData.giftPoster" alt="" class="gift-img" />
        <div class="btn_box">
          <div class="btn-to-lock" @click="toLock" v-if="userType === 0" v-click-track="'ljcy'"></div>
          <div class="btn-to-buy" @click="toLock" v-if="userType === 1 || userType === 2" v-click-track="'ysqqxd'"></div>
        </div>
      </div>
      <div>
        <img :src="furnish.step" alt="" class="step-img" />
      </div>
      <div class="sku-title"></div>
      <div class="sku-box">
        <div class="sku-item" v-for="(item,index) in skuList" :key="index">
          <img class="sku-img" @click="gotoSkuPage(item.skuId)" :src=item.skuMainPicture alt="">
        </div>
        <div class="more-btn-all" v-if="skuList.length && skuList.length !== total">
          <div class="more-btn" @click="loadMore">点我加载更多</div>
        </div>
      </div>
    </div>
  </div>

  <van-popup v-model:show="rulePopup" :closeOnClickOverlay="false">
      <Rule @close="rulePopup = false"></Rule>
  </van-popup>
  <van-popup v-model:show="recordPopup" :closeOnClickOverlay="false">
      <Record v-if="recordPopup" @close="recordPopup = false"></Record>
  </van-popup>
  <van-popup v-model:show="strategyPopup" :closeOnClickOverlay="false">
      <Strategy @close="strategyPopup = false"></Strategy>
  </van-popup>
  <!-- 活动门槛 -->
  <ThresholdCPB v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
</template>

<script lang="ts" setup>
import { inject, onMounted, onUnmounted, ref } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import furnishStyles, { furnish } from './ts/furnishStyles';
import usePostMessage from '@/hooks/usePostMessage';
import Rule from './components/Rule.vue';
import Record from './components/Record.vue';
import Strategy from './components/Strategy.vue';
import ThresholdCPB from './components/ThresholdCPB.vue';
import useThreshold from '@/hooks/useThreshold';
import { checkThreshold } from '@/components/Threshold/ts/logic';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { getSkuList, pollingPayStatus, userType, actData, strategyPopup, skuList, showLimit, loadMore, total } from './ts/logic';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import { httpRequest } from '@/utils/service';
import constant from '@/utils/constant';
import dayjs from 'dayjs';
import './css/cpbStyle.scss';

const { registerHandler } = usePostMessage();

const decoData = inject('decoData') as DecoData;
const baseInfo: any = inject('baseInfo') as BaseInfo;

showLimit.value = useThreshold({
  thresholdList: baseInfo.thresholdResponseList,
  className: 'message-cpb',
});

// 规则弹窗
const rulePopup = ref(false);
// 锁权记录弹窗
const recordPopup = ref(false);

const needCheckPay = ref(false);

// 支付
const toPay = async () => {
  if (!checkThreshold(baseInfo)) return;
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90006/pay', {
      activityId: baseInfo.activityMainId,
      amount: 1,
      shopId: baseInfo.shopId,
      source: '01',
      token: sessionStorage.getItem(constant.LZ_JD_TOKEN),
    });
    closeToast();
    const param = {
      orderId: data.orderId,
      paySign: data.paySign,
      returnUrl: encodeURIComponent(window.location.href),
    };
    // sessionStorage.setItem('merchantOrderId', data.merchantOrderId);
    const payUrl = `openapp.jdmobile://virtual?params={"category":"jump","des":"jdmp","appId":"2B43F9A518AE09BAE8789053047A685E","vapptype":"1","path":"pages/saas-pay/saas-pay.html","pageAlias":"","param":${JSON.stringify(param)}}`;
    needCheckPay.value = true;
    window.location.href = payUrl;
    console.log(payUrl);
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast({
        message: error.message,
      });
    }
  }
};

// 检查支付状态
const checkPay = () => {
  if (!needCheckPay.value) return;
  needCheckPay.value = false;
  pollingPayStatus();
};

onMounted(() => {
  // 从上一页返回时，重新获取数据
  document.addEventListener('visibilitychange', checkPay);
});
onUnmounted(() => {
  document.removeEventListener('visibilitychange', checkPay);
});

// 点击锁权
const toLock = async () => {
  if (!checkThreshold(baseInfo)) {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
      className: 'message-cpb',
    });
    return;
  }
  if (dayjs().isBefore(actData.value.promotionStartTime)) {
    showToast('0.01元锁权未开始');
    return;
  }
  if (dayjs().isAfter(actData.value.promotionEndTime)) {
    showToast('0.01元锁权已结束');
    return;
  }
  closeToast();
  if (userType.value === 0) {
    // 去支付0.01元
    await toPay();
  }
  // 已经支付（锁权成功）
  if (userType.value === 1) {
    // TODO:去下单,修改成链接，先暂时跳店铺首页
    // await gotoShopPage(actData.value.shopId);
    window.location.href = furnish.linkUrl;
  }
  // 已经下单（令牌已经使用)
  if (userType.value === 2) {
    showToast('您已经下过单了~');
  }
};

// 拉sku列表数据
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getSkuList()]);
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};
init();
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
<style lang="scss">
@font-face {
  font-family: 'EnglishFont';
  src: url('./font/Cronos.ttf') format('truetype');
}

@font-face {
  font-family: 'ChineseFont';
  src: url('./font/fzxh.TTF') format('truetype');
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.54rem 0rem 0.3rem 0.3rem;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0;
  }

  .header-btn {
    width: 1.1rem;
    height: 0.36rem;
    line-height: 0.38rem;
    margin-bottom: 0.1rem;
    font-size: 0.19rem;
    text-align: center;
    border-radius: 0.23rem 0 0 0.23rem;
    background-size: 100%;
    background-repeat: no-repeat;
    font-family: 'EnglishFont', 'ChineseFont', sans-serif;
  }
}
.blank {
  //padding: 0.3rem 0;
}
.exchange-area {
  border-radius: 0.1rem;
  //margin: 0 0 0.5rem;
  margin: 0 auto;
  .gift-img {
    width: 100%;
    //margin-bottom: 0.2rem;
  }
  .btn_box{
    position: relative;
  }
  .btn-to-lock {
    background: url(//img20.360buyimg.com/imgzone/jfs/t1/99268/8/50081/10215/66f11088Fb3ddc8ee/df3e73f92dd4410a.png) no-repeat;
    background-size: 100%;
    width: 2.15rem;
    height: 0.5rem;
    margin: 0 auto;
    font-size: 0.28rem;
    text-align: center;
    line-height: 0.65rem;
    color: #fff;
    position: absolute;
    top: -1.8rem;
    left: 2.675rem;
  }
  .btn-to-buy{
    background:url('//img10.360buyimg.com/imgzone/jfs/t1/165264/22/42607/14467/66120180Fde84803d/ba88ffc1b9243c4e.png') no-repeat;
    background-size: 100%;
    width: 3.49rem;
    height: 0.63rem;
    margin: 0 auto;
    font-size: 0.28rem;
    text-align: center;
    line-height: 0.65rem;
    color: #fff;
    position: absolute;
    top: -1.8rem;
    left: 2.005rem;
  }
  .tip {
    text-align: center;
    font-size: 0.14rem;
    color: #000;
  }
}
.segmentation {
  width: 100%;
  margin-bottom: 0.3rem;
}

.step-img {
  width: 7.5rem;
  //margin-bottom: 0.2rem;
}
.sku-title{
  width: 7.47rem;
  height: 1.2rem;
  background: url("//img10.360buyimg.com/imgzone/jfs/t1/87781/39/49512/78587/66f11544F9d90c21c/19c5345a221c9611.png") no-repeat;
  background-size: 100%;
}

.sku-box{
  width: 6.9rem;
  height: 6.4rem;
  overflow: hidden;
  overflow-y: scroll;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding:0 0.2rem 0 0.2rem;
  margin: 0 auto;
  .more-btn-all{
    width:100%;
    display:flex;
    justify-content:center;
    margin-top:0.24rem;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #fff;
      background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto 0.3rem;
    }
  }
  .sku-item{
    display: flex;
    flex-direction: column;
    width: 3rem;
    height: 3rem;
    margin: 0 0.1rem 0.2rem 0.1rem;
    .sku-img{
      background: url("//img10.360buyimg.com/imgzone/jfs/t1/233292/2/15604/5617/6612017dF31ddb359/2754908b4010e402.png") no-repeat;
      background-size: 100%;
      width: 3rem;
      height: 3rem;
      border-radius: 0.3rem;
    }
  }
}
</style>
