<template>
  <div class="card-bk">
    <div class="close" @click="close"></div>
    <div class="content">
      <img :src="detail.showImg" alt="" class="card-img" />
      <div class="prize-name">{{ detail.prizeName }}</div>
      <div class="item" v-show="detail.cardNumber">
        <div class="label">卡号：</div>
        <div class="text">{{ detail.cardNumber }}</div>
        <div class="copy-btn" :copy-text="detail.cardNumber">复制</div>
      </div>
      <div class="item" v-show="detail.cardPassword">
        <div class="label">卡密：</div>
        <div class="text">{{ detail.cardPassword }}</div>
        <div class="copy-btn" :copy-text="detail.cardPassword">复制</div>
      </div>
      <div class="tip">礼品卡说明：</div>
      <div class="tip" :class="{ 'tip-small': detail.cardNumber && detail.cardPassword }">{{ detail.cardDesc }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';
import { CardType } from '../ts/type';
import Clipboard from 'clipboard';
import { showToast } from 'vant';

const props = defineProps({
  detail: {
    type: Object as PropType<CardType>,
    required: true,
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });
</script>

<style scoped lang="scss">
.card-bk {
  position: relative;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/183338/23/18385/79738/61109180E22c23a60/43dc79760af42b09.png) no-repeat;
  background-size: 100%;
  width: 6rem;
  height: 8.24rem;
  padding: 1.65rem 0.2rem 0.2rem;
  .content {
    padding: 0 0.35rem;
    .card-img {
      margin: 0 auto;
      width: 1.5rem;
    }
    .prize-name {
      color: #e2231a;
      width: 100%;
      text-align: center;
      font-weight: bolder;
      margin-top: 0.2rem;
      margin-bottom: 0.2rem;
      font-size: 0.36rem;
    }
    .item {
      display: flex;
      align-items: center;
      font-size: 0.3rem;
      margin-bottom: 0.2rem;
      .label {
        color: #ffdb6e;
        width: 1rem;
      }
      .text {
        flex: 1;
        color: #ff5f00;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        border: 0.03rem solid #ffdb6e;
        padding: 0 0.1rem;
        line-height: 0.45rem;
        border-radius: 0.15rem 0 0 0.15rem;
      }
      .copy-btn {
        width: 1rem;
        line-height: 0.45rem;
        text-align: center;
        color: #fff;
        font-size: 0.3rem;
        border-radius: 0 0.15rem 0.15rem 0;
        border: 0.03rem solid #ffdb6e;
        background-color: #ffdb6e;
      }
    }
    .tip {
      font-size: 0.2rem;
      color: #a3a3a3;
      white-space: pre-line;
      max-height: 3rem;
      overflow-y: scroll;
    }
    .tip-small {
      max-height: 2.3rem;
    }
  }
  .close {
    position: absolute;
    width: 0.6rem;
    height: 0.6rem;
    top: 0.65rem;
    right: 0.1rem;
  }
}
</style>
