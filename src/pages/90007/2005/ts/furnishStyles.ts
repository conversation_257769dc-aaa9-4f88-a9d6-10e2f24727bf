import { computed, reactive } from 'vue';

export const furnish = reactive({
  pageBg: '',
  actBg: '', // 主页背景图
  actBgColor: '', // 主页背景色
  shopNameColor: '', // 店铺名称颜色
  btnColor: '', // 按钮字体颜色
  btnBg: '', // 按钮背景颜色
  btnBorderColor: '', // 按钮边框颜色
  acIntroductionBg: '', // 活动介绍背景图
  seriesGoodsBg: '', // 系列图
  seriesBoxBkBody: '',
  seriesBoxBkFooter: '',
  seriesBoxBkHead: '',
  buyBtnBg: '', // 继续集罐按钮
  exchangeBtn: '', // 兑换按钮
  skuBg: '', // 曝光商品标题
  winnersBg: '', // 中奖名单背景图
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const headerBtn = computed(() => ({
  color: furnish.btnColor ?? '',
  backgroundColor: furnish.btnBg ?? '',
  borderColor: furnish.btnBorderColor ?? '',
}));

const acIntroductionBg = computed(() => ({
  backgroundImage: furnish.acIntroductionBg ? `url("${furnish.acIntroductionBg}")` : '',
}));

const skuBg = computed(() => ({
  backgroundImage: furnish.winnersBg ? `url("${furnish.winnersBg}")` : '',
}));
const seriesGoodsBg = computed(() => ({
  backgroundImage: furnish.seriesGoodsBg ? `url("${furnish.seriesGoodsBg}")` : '',
}));

const buyBtnBg = computed(() => ({
  backgroundImage: furnish.buyBtnBg ? `url("${furnish.buyBtnBg}")` : '',
}));
const exchangeBtn = computed(() => ({
  backgroundImage: furnish.exchangeBtn ? `url("${furnish.exchangeBtn}")` : '',
}));

export default {
  pageBg,
  shopNameColor,
  headerBtn,
  acIntroductionBg,
  seriesGoodsBg,
  buyBtnBg,
  exchangeBtn,
  skuBg,
};
