<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="kv">
      <div class="btn-list">
        <img :src="furnish.ruleBtn" alt="" @click="showRulePopup" />
        <img :src="furnish.myPrizeBtn" alt="" @click="myPrizePopup = true" />
        <img :src="furnish.orderBtn" alt="" @click="orderRecordPopup = true" />
      </div>
    </div>
    <div class="series-list" :style="furnishStyles.seriesBg.value" v-for="(item, index) in seriesList" :key="index">
      <div class="series-title" :style="furnishStyles.seriesTitleColor.value">{{ item.seriesName }}满额礼</div>
      <div class="user-info">
        <img alt="" :src="userInfo.avatar || 'https://img10.360buyimg.com/imgzone/jfs/t20782/85/568972117/2758/78eafc28/5b0fd507Ne2b074cc.png'" />
        <div class="buy-amount" :style="furnishStyles.bugAmountColor.value">您已购买{{ item.seriesAmount || 0 }}元</div>
      </div>
      <div class="step-sc">
        <div class="step-list">
          <div class="step" :style="getIsAct(actStep[index] ?? 0, idx, index) ? furnishStyles.stepBtnSelectBg.value : furnishStyles.stepBtnBg.value" @click="changeActStep(index, idx)" v-for="(step, idx) in item.stepList" :key="idx">
            <svg class="step-svg" width="1.84rem" height="0.85rem">
              <text x="50%" y="55%" text-anchor="middle" :style="furnishStyles.stepText.value">
                {{ step.stepName }}
              </text>
            </svg>
          </div>
        </div>
      </div>
      <div class="prize-list swiper-container">
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="(it, idx) in item.stepList[actStep[index] ?? 0].prizeList" :key="`${it.prizeKey}${idx}`">
            <div class="prize-item" :style="furnishStyles.prizeBg.value">
              <img :src="it.prizeImg" alt="" class="prize-img" />
              <div class="prize-info">
                <div class="name">{{ it.prizeName }}</div>
                <div class="now-price">0元兑换</div>
                <div class="price">价值：{{ it.unitPrice }}</div>
                <div class="surplus">剩余{{ it.sendTotalCount - it.sendCount }}份</div>
              </div>
              <img :src="furnish.exchangeBtn" alt="" class="exchange-btn" :class="{ gray: it.status !== 1 }" @click="exchangePrize(it)" />
            </div>
          </div>
        </div>
      </div>
      <div>
        <div class="pre"></div>
        <div class="next"></div>
      </div>
    </div>

    <div class="sku-content" :style="furnishStyles.skuListBg.value">
      <div class="step-sc">
        <div class="step-list">
          <div class="step" :style="getSkuIsAct(skuActStep, idx) ? furnishStyles.stepBtnSelectBg.value : furnishStyles.stepBtnBg.value" @click="changeSkuActStep(idx)" v-for="(item, idx) in seriesList" :key="idx">
            <svg class="step-svg" width="1.84rem" height="0.85rem">
              <text x="50%" y="55%" text-anchor="middle" :style="furnishStyles.stepText.value">
                {{ item.seriesName }}
              </text>
            </svg>
          </div>
        </div>
      </div>
      <div class="sku-sc">
        <div class="sku-list">
          <div class="sku-item" v-for="(item, index) in showSkuList" :key="index" :style="furnishStyles.skuBg.value">
            <img :src="item.skuMainPicture" alt="" class="sku-img" />
            <div class="sku-text" :style="furnishStyles.skuTitleBg.value">{{ item.skuName }}</div>
            <img :src="furnish.goSkuBtn" alt="" class="btn" @click="gotoSkuPage(item.skuId)" />
          </div>
          <div class="more-btn" v-if="showSkuList.length < pageInfo.pagesAll" @click="loadMore">点我加载更多</div>
        </div>
      </div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="rulePopup">
    <Rule @close="rulePopup = false" :rule="ruleText"></Rule>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="myPrizePopup" :closeOnClickOverlay="false">
    <MyPrize v-if="myPrizePopup" @close="myPrizePopup = false"></MyPrize>
  </VanPopup>
  <!--我的订单弹窗-->
  <VanPopup teleport="body" v-model:show="orderRecordPopup" :closeOnClickOverlay="false">
    <OrderRecordPopup @close="orderRecordPopup = false"></OrderRecordPopup>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="saveAddressPopup" :closeOnClickOverlay="false">
    <SaveAddress v-if="saveAddressPopup" @close="saveAddressPopup = false" :userPrizeId="awardPrize.userPrizeId"></SaveAddress>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="awardPopup" :closeOnClickOverlay="false">
    <Award v-if="awardPopup" @close="awardPopup = false" :prize="awardPrize" @saveAddress="toSaveAddress"></Award>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="joinPopup" :closeOnClickOverlay="false">
    <OpenCard @close="joinPopup = false"></OpenCard>
  </VanPopup>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, inject } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import furnishStyles, { furnish, taskRequestInfo } from './ts/furnishStyles';
import Swiper from 'swiper';
import 'swiper/swiper.min.css';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { httpRequest } from '@/utils/service';
import { gotoSkuPage } from '@/utils/platforms/jump';
import MyPrize from './components/MyPrize.vue';
import Rule from './components/Rule.vue';
import Award from './components/AwardPopup.vue';
import SaveAddress from './components/SaveAddress.vue';
import OrderRecordPopup from './components/OrderRecordPopup.vue';
import OpenCard from './components/OpenCard.vue';
import { UserInfo } from '@/utils/products/types/UserInfo';

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const userInfo = inject('userInfo') as UserInfo;

const isLoadingFinish = ref(false);
const rulePopup = ref(false);
const myPrizePopup = ref(false);
const orderRecordPopup = ref(false);
const saveAddressPopup = ref(false);
const awardPopup = ref(false);
const joinPopup = ref(false);

// 展示活动规则，首次获取规则
const ruleText = ref('');
const showRulePopup = async () => {
  try {
    if (!ruleText.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleText.value = data;
    }
    rulePopup.value = true;
  } catch (error: any) {
    console.error();
  }
};

const seriesList = ref<any[]>([]);
const showSkuList = ref<any[]>([]);
const pageInfo = reactive({
  pageNum: 1,
  pageSize: 10,
  pagesAll: 0,
});

const actStep = ref<number[]>([]);

const swiperList: Swiper[] = [];
const initSwiper = () => {
  swiperList.forEach((item) => {
    item.destroy();
  });
  swiperList.length = 0;
  nextTick(() => {
    const prizeList = document.querySelectorAll('.prize-list');
    prizeList.forEach((item) => {
      swiperList.push(
        new Swiper(item, {
          spaceBetween: 10,
          observeSlideChildren: true,
          observer: true,
        }),
      );
    });
  });
};

const getIsAct = (actStepIndex: number, idx: number) => actStepIndex === idx;

const changeActStep = (index: number, idx: number) => {
  actStep.value[index] = idx;
};

const skuActStep = ref(0);
const getSkuIsAct = (skuAct: number, idx: number) => skuAct === idx;

const checkMember = () => {
  if (baseInfo.status === 1) {
    showToast('活动未开始');
    return false;
  }
  if (baseInfo.status === 3) {
    showToast('活动已结束');
    return false;
  }
  if (baseInfo.memberLevel <= 0) {
    joinPopup.value = true;
    return false;
  }
  return true;
};

// 获取曝光商品
const getExposureSku = async (seriesId: string) => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90012/getSkuListPage', {
      seriesId,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    });
    closeToast();
    showSkuList.value.push(...res.data.records);
    pageInfo.pagesAll = res.data.total;
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};
const loadMore = async () => {
  pageInfo.pageNum++;
  const { seriesId } = seriesList.value[skuActStep.value];
  await getExposureSku(seriesId);
};

const changeSkuActStep = async (idx: number) => {
  if (skuActStep.value === idx) return;
  showSkuList.value = [];
  pageInfo.pageNum = 1;
  const { seriesId } = seriesList.value[idx];
  getExposureSku(seriesId);
  skuActStep.value = idx;
};

const getSeriesPrizes = async () => {
  try {
    const { data } = await httpRequest.post('/90012/getSeriesPrizes');
    if (actStep.value.length !== data.length) {
      actStep.value.length = data.length;
      actStep.value.fill(0);
    }
    seriesList.value = data;
  } catch (error: any) {
    console.error(error);
  }
};

const awardPrize = ref({
  prizeType: 3,
  prizeName: '11',
  showImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/231005/7/25170/16188/66c6b459F39423e29/4f23f94c0f41fc7f.png',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});
// 兑换奖品
const exchangePrize = async (item: any) => {
  if (!checkMember()) return;
  if (item.status === 2) {
    showToast('您已兑换过该奖品');
    return;
  }
  if (item.status === 3) {
    showToast('你未达到兑换条件');
    return;
  }
  try {
    showLoadingToast({
      message: '兑换中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90012/receivePrize', {
      prizeId: item.prizeId,
    });
    awardPrize.value.prizeName = item.prizeName;
    awardPrize.value.prizeType = item.prizeType;
    awardPrize.value.showImg = item.prizeImg;
    awardPrize.value.userPrizeId = data.userPrizeId;
    awardPopup.value = true;
    closeToast();
    getSeriesPrizes();
  } catch (error: any) {
    console.error(error);
    showToast(error.message);
  }
};

const toSaveAddress = () => {
  saveAddressPopup.value = true;
  awardPopup.value = false;
};

const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await getSeriesPrizes();
    await getExposureSku(seriesList.value[0].seriesId);
    isLoadingFinish.value = true;
    initSwiper();
    closeToast();
    checkMember();
  } catch (error: any) {
    console.error(error);
    closeToast();
  }
};
init();
setTimeout(() => {
  getSeriesPrizes();
}, 5000);
</script>

<style>
::-webkit-scrollbar {
  width: 0 !important;
  display: none;
  height: 0;
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  position: relative;
  padding-bottom: 0.35rem;
  .kv {
    height: 8.66rem;
    display: flex;
    justify-content: flex-end;
    padding-top: 0.2rem;
    .btn-list img {
      width: 1.1rem;
    }
  }
  .series-list {
    position: relative;
    width: 6.8rem;
    height: 7.8rem;
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 0 auto;
    .series-title {
      font-size: 0.4rem;
      text-align: center;
      padding-top: 0.1rem;
      line-height: 0.9rem;
      font-weight: bold;
      margin-bottom: 0.4rem;
    }
    .user-info {
      display: flex;
      align-items: center;
      padding: 0 0.81rem;
      height: 1.52rem;
      margin-bottom: 0.25rem;
      img {
        width: 1.13rem;
        height: 1.13rem;
        border-radius: 50%;
        background-color: #fff;
        margin-left: 0.2rem;
      }
      .buy-amount {
        font-size: 0.285rem;
        color: #fff;
        margin-left: 0.2rem;
      }
    }
    .prize-list {
      width: 5.42rem;
      margin: 0 auto;
      overflow: hidden;
      background-size: 100%;
      background-repeat: no-repeat;
    }
    .prize-item {
      position: relative;
      width: 5.42rem;
      height: 2.95rem;
      background-size: 100%;
      background-repeat: no-repeat;
      display: flex;
      align-items: center;
      padding: 0 0.2rem 0.37rem;
      .prize-img {
        width: 2rem;
        height: 2rem;
        border-radius: 0.2rem;
      }
      .prize-info {
        flex: 1;
        padding-left: 0.2rem;
        .name {
          font-size: 0.27rem;
          color: #7f3d01;
          margin-bottom: 0.15rem;
          word-break: break-all;
          font-weight: bold;
        }
        .now-price {
          font-size: 0.24rem;
          color: #bd0401;
          font-weight: bold;
          margin-bottom: 0.1rem;
        }
        .price {
          font-size: 0.21rem;
          color: #7f3d01;
          // 中间线
          text-decoration: line-through;
        }
        .surplus {
          font-size: 0.15rem;
          color: #d3a54f;
        }
      }
      .exchange-btn {
        position: absolute;
        bottom: 0;
        // 左右居中
        left: 50%;
        transform: translateX(-50%);
        width: 2.25rem;
      }
    }
    .pre {
      position: absolute;
      top: 5.5rem;
      left: 0;
      width: 0.3rem;
      height: 0.5rem;
    }
  }
  .sku-content {
    position: relative;
    width: 6.8rem;
    height: 13.45rem;
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 0 auto;
    padding-top: 1.7rem;
    .sku-sc {
      overflow-y: scroll;
      height: 10.4rem;
    }
    .sku-list {
      width: 6.2rem;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .sku-item {
        position: relative;
        width: 3rem;
        height: 3rem;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-bottom: 0.3rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .sku-img {
        width: 2.9rem;
        height: 2.9rem;
        border-radius: 0.3rem;
      }
      .sku-text {
        position: absolute;
        left: 0;
        top: 0;
        background-repeat: no-repeat;
        background-size: 100%;
        width: 2.06rem;
        height: 0.48rem;
        white-space: nowrap;
        font-size: 0.18rem;
        line-height: 0.48rem;
        text-align: center;
        text-overflow: ellipsis;
        overflow: hidden;
        padding-left: 0.05rem;
        padding-right: 0.25rem;
      }
      .btn {
        position: absolute;
        bottom: 0.15rem;
        left: 50%;
        transform: translateX(-50%);
        width: 2rem;
      }
      .more-btn {
        text-align: center;
        font-size: 0.2rem;
        color: #a86117;
        width: 100%;
      }
    }
  }
}

.step-sc {
  width: 6.2rem;
  margin: 0 auto;
  overflow-x: auto;
  overflow-y: hidden;
}
.step-list {
  width: fit-content;
  min-width: 6.2rem;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  height: 1.15rem;
}
.step {
  width: 1.84rem;
  height: 0.85rem;
  background-size: 100%;
  background-repeat: no-repeat;
  text-align: center;
  padding-top: 0.05rem;
  line-height: 0.8rem;
  font-size: 0.25rem;
  .step-svg {
    width: 100%;
    height: 100%;
    stroke-linejoin: round;
    font-weight: bold;
    vertical-align: middle;
  }
}
.gray {
  filter: grayscale(1);
}
</style>
