<template>
  <div class="myprize-bk" v-if="!prizeList?.length" :style="furnishStyles.myPrizeDialogBg.value">
    <div class="content">
      <div class="list">
        <div class="no-data">暂无数据</div>
      </div>
    </div>
    <div class="close" @click="closeMyPrize"></div>
  </div>
  <div v-else class="myprize-bk" :style="furnishStyles.myPrizeDialogBg.value">
    <div class="content">
      <!--      <div v-if="!hasAddress"  class="address-btn" @click="saveAddress">填写地址</div>-->
      <!--      <div v-else class="address-btn" @click="previewAddress">查看地址</div>-->
      <div class="list">
        <div class="item" v-for="(item, index) in prizeList" :key="index">
          <div class="prize-name">{{ item.prizeName }}</div>
          <div class="time">{{ dayjs(item.createTime).format('YYYY-MM-DD') }}</div>
          <div class="status">
            <span style="margin-right: 0.1rem" @click="handleEditAddress(item)">{{ getStatus(item.status) }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="close" @click="closeMyPrize"></div>
  </div>
  <VanPopup teleport="body" v-model:show="addressPopup">
    <SaveAddress v-if="addressPopup" :isPreview="isCheck" :echoInfo="form" @close="closeAddress" :isUpdate="isUpdate"></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { reactive, ref } from 'vue';
import dayjs from 'dayjs';
import SaveAddress from './SaveAddress.vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { isPreview } from '@/utils';

const emits = defineEmits(['close']);
const addressPopup = ref(false);
const isCheck = ref(false);
const isUpdate = ref(0);

const form = reactive({
  address: '',
  city: '',
  county: '',
  hasAddress: true,
  mobile: '',
  postalCode: '',
  province: '',
  realName: '',
  userPrizeId: '',
});
const closeMyPrize = () => {
  emits('close');
};

const handleEditAddress = (item: any) => {
  Object.keys(form).forEach((key) => {
    form[key] = item[key];
  });
  // 修改 status=0 可以填写地址  isPreview：判断显示 确定/领奖按钮 isUpdate：判断调用更新地址/领奖接口
  if (item.status === 0) {
    isUpdate.value = 1;
    isCheck.value = false;
    addressPopup.value = true;
  } else if (item.status === 1) {
    // 查看 status=1
    isUpdate.value = 1;
    isCheck.value = true;
    addressPopup.value = true;
  }
};

const getStatus = (val: number) => {
  if (val === 0) {
    return '修改地址';
  }
  if (val === 1) {
    return '查看地址';
  }
  if (val === 2) {
    return '地址不存在';
  }
  if (val === 3) {
    return '已发货';
  }
  return '代发货';
};

const prizeList = ref<any[]>([]);

const getMyPrize = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90014/getUserPrizes');
    console.log('data', data);
    prizeList.value = data;
    closeToast();
  } catch (error: any) {
    closeToast();
    showToast(error.message);
  }
};

!isPreview && getMyPrize();

const closeAddress = (isSuccess: boolean) => {
  if (isSuccess) {
    getMyPrize();
    // getUserAddressInfo();
  }
  addressPopup.value = false;
};
</script>

<style scoped lang="scss">
::-webkit-scrollbar {
  display: none;
}
.myprize-bk {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/256256/32/13453/126502/67889e8eFe68fe6a9/ca7cbe4a5e0247df.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 6.48rem;
  height: 8rem;
  padding-top: 1.6rem;
  padding-bottom: 0.3rem;
  position: relative;
  .content {
    width: 5.92rem;
    height: 2.7rem;
    position: absolute;
    left: 50%;
    transform: translate(-50%);
    top: 2rem;
    .list {
      width: 5.92rem;
      height: 2.7rem;
      overflow-y: auto;
      .item {
        width: 100%;
        display: flex;
        align-items: center;
        font-size: 0.24rem;
        text-align: center;
        margin: 0.2rem 0;
        justify-content: space-around;
        color: #f13033;
        font-weight: 600;
        .prize-name {
          text-align: left;
          padding-left: .2rem;
          flex: 1;
        }
        .time {
          flex: 1;
        }
        .status {
          flex: 1;
          text-align: right;
        }
      }
    }
    .no-data {
      font-size: 0.4rem;
      color: #f13033;
      font-weight: 600;
      text-align: center;
      margin: 1rem auto 0;
    }
  }
}
.close {
  width: 0.9rem;
  height: 0.9rem;
  position: absolute;
  left: 50%;
  transform: translate(-50%);
  bottom: 0.3rem;
  //background: #2d8cf0;
}
</style>
