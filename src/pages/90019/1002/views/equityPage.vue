<!--
 * @actName:
 * @author: <PERSON><PERSON><PERSON> lin
-->
<template>
  <!-- result-background -->
  <div class='page-view'>
    <img class='logo' src='../assets/img/logo-icon.png' alt=''>

    <img class='result-title' src='../assets/img/equity-title.png' alt=''>
    <div class="equity-view-all">
      <div class='equity-view' :style="seriesPrizeList.length >= 3 ? {} : { justifyContent: 'space-around' }">
        <div class='equity-item' v-for='(item,index) in seriesPrizeList' :key='index'>
          <div class="seriesTabDivAll">
            <div :class="[currentSeriesTab === index ? 'seriesTabDivActive' : 'seriesItemTabDiv']" @click='currentSeriesTab=index'>{{item.seriesName}}</div>
          </div>
        </div>
      </div>

      <div class="itemPrizeListDivAll">
        <div class="itemPrizeListDiv" v-for="(item1,index1) in seriesPrizeList[currentSeriesTab].couponInfos" :key="index1">
          <div :class="[currentEquityIndex === index1 ? 'prizePictureDivActive' : 'prizePictureDiv']" @click='chooseEquity(item1,index1)'>
            <img :src='item1.prizeImg' alt=''>
          </div>
          <div class='equity-name'>{{item1.prizeName}}</div>
        </div>
      </div>

    </div>
    <img class='result-btn' src='../assets/img/sure-btn.png' v-click-track="'ljlq-new2'" @click="handleResult('')" alt=''>
    <img class="bottom" src="../assets/img/bottom-text.png" alt="">

  </div>

  <VanPopup teleport="body" v-model:show="showSureDrawDialog" :close-on-click-overlay="false">
    <SureDrawDialog :isHc="mutualExclusion" @closeDialog='showSureDrawDialog= false' @sureDraw='sureDraw'></SureDrawDialog>
  </VanPopup>
</template>

<script lang='ts' setup>
import { ref, inject, computed } from 'vue';
import { showToast, closeToast, showLoadingToast } from 'vant';
import { BaseInfo } from '@/types/BaseInfo';
import { getDataInterface } from '../ts/port';
import SureDrawDialog from '../components/SureDrawDialog.vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { httpRequest } from '@/utils/service';

const router = useRouter();

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

const store = useStore();
const currentSeriesTab = ref(0); // 默认选中tab
const seriesPrizeList = ref([{ couponInfos: [] }]);
// 手机号
const successPhone = computed(() => store.getters.getSuccessPhone);
const mutualExclusion = computed(() => store.getters.getMutualExclusion); // 是否与0元试喝互斥
// console.log(mutualExclusion.value, 'mutualExclusion==========');
const showSureDrawDialog = ref(false);
const currentEquityIndex = ref();
const chooseEquity = async (e: any, index: number) => {
  if (!e.prizeStock) {
    showToast('该权益已领完，请重新选择');
    return;
  }
  currentEquityIndex.value = index;
};

const handleResult = () => {
  if (!(currentEquityIndex.value >= 0)) {
    showToast('请先选择权益奖品');
    return;
  }
  showSureDrawDialog.value = true;
};

const sureDraw = async () => {
  showSureDrawDialog.value = false;

  // 领取权益
  const res = await getDataInterface('sendNewGift2Prize', 'post', {
    prizeId: seriesPrizeList.value[currentSeriesTab.value].couponInfos[currentEquityIndex.value].prizeId,
    mobile: successPhone.value,
  });
  // status 0 未中奖  1 中奖
  if (res.data.status === 1) {
    console.log('领取成功');
    store.commit('setUserType', 'pass');
    store.commit('setPrizeList', {
      prizeName: res.data.prizeName,
      prizeImg: res.data.prizeImg,
      skuId: res.data.skuId,
    });
    await router.replace({ path: '/result' });
  } else {
    console.log(res, '领取失败');
    store.commit('setUserType', 'fail');
    await router.replace('/result');
  }
};
// 获取新客权益2奖品信息
const getNewPrizeInfo = async () => {
  const res = await getDataInterface('getNewGift2PrizeInfo', 'post');
  seriesPrizeList.value = res.data;
  if (res.data) {
    seriesPrizeList.value = res.data;
  }
};
getNewPrizeInfo();
</script>
<style>

*::-webkit-scrollbar {
  width: 0 !important;
}
::-webkit-scrollbar {
  display: none;
  width: 0;
}
&::-webkit-scrollbar {
  display: none;
}
</style>
<style lang='scss'>
.page-view {
  width: 100%; /* 宽度自适应 */
  min-height: 100vh;
  padding: 2.2rem 0 0.2rem;
  position: relative;
  text-align: center;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/281303/24/4269/45429/67d9437eF2a2c7784/9fe50b4d988ce4ac.png");
    repeat: no-repeat;
    size: 100% 100%;
  };

  .logo {
    width: 1.35rem;
    position: absolute;
    top: .5rem;
    left: 50%;
    transform: translateX(-50%);
  }

  .result-title {
    width: 5.21rem;
    margin: 0 auto;
  }
  .equity-view-all{
    width: 100%;
    margin: 0.78rem auto 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    .equity-view {
      display: flex;
      //align-items: center;
      //justify-content: space-around;
      //justify-content: center;
      overflow-x: scroll;
      width: 6.7rem;
      margin: auto auto 1rem;
      .equity-item {
        text-align: center;
        color: #0033be;
        .seriesTabDivAll{
          display: flex;

          .seriesItemTabDiv{
            color: #fff;
            font-size: 0.28rem;
            width:2.22rem;
            height:0.83rem;
            line-height: 0.6rem;
            background: {
              image: url("../assets/img/权益未选中系列tab.png");
              repeat: no-repeat;
              size: 100% 100%;
            };
          }
          .seriesTabDivActive{
            color: #fff;
            font-size: 0.28rem;
            width:2.22rem;
            height:0.83rem;
            line-height: 0.6rem;
            background: {
              image: url("../assets/img/权益选中系列tab.png");
              repeat: no-repeat;
              size: 100% 100%;
            };
          }
        }
        .equity-bg-active {
          background: {
            image: url("../assets/img/equity-item-active.png") !important;
          };
        }
      }
    }
    .itemPrizeListDivAll{
      width: 6.7rem;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .itemPrizeListDiv{
        //flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom:0.3rem;
        .prizePictureDiv{
          width: 2.94rem;
          height: 2.81rem;
          display: flex;
          padding-top: 0.1rem;
          justify-content: center;
          background: {
            image: url("../assets/img/权益图片.png");
            repeat: no-repeat;
            size: 100% 100%;
          };
          img{
            //width: 2.5rem;
            height: 2.4rem;
            border-radius: 0.2rem
          }
        }
        .prizePictureDivActive{
          width: 2.94rem;
          height: 2.81rem;
          display: flex;
          padding-top: 0.1rem;
          justify-content: center;
          background: {
            image: url("../assets/img/权益图片选中.png");
            repeat: no-repeat;
            size: 100% 100%;
          };
          img{
            //width: 2.5rem;
            height: 2.4rem;
            border-radius: 0.2rem
          }
        }
        .equity-name{
          color: #9d5322;
          font-size: 0.3rem;
          margin-top:0.1rem;
        }
      }
      .equity-bg {
        margin: 0 auto;
        width: 2.84rem;
        height: 2.84rem;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 0.2rem 0 0;
        background: {
          image: url("../assets/img/equity-item.png");
          repeat: no-repeat;
          size: contain;
        };
        img {
          width: 2.3rem;
        }
      }
    }
  }
  .result-btn {
    width: 2.69rem;
    margin: 1.35rem auto 0;
  }
  .bottom{
    margin: 3rem auto 0;
    width: 7.2rem;
  }
}

</style>
