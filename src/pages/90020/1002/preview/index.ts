import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const _decoData = {
  actBg: '',
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/276294/14/21232/124901/67fdcb0aFa056f24f/513256eeddfbf986.png',
  actBgColor: '',
  stepImg: '//img10.360buyimg.com/imgzone/jfs/t1/272648/3/21448/56220/67fdc97bF3ae8d939/8ea8c20c7e006a06.png',
  canNotJoinKv: '//img10.360buyimg.com/imgzone/jfs/t1/281799/28/4708/120190/67d94048F24e9b5d0/295702578ec8c9c9.png',
  disableShopName: 0,
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/279736/6/4865/17795/67d94047F2d31151b/0399d5a968b2d784.png',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/272871/10/5347/7864/67d94047F314308e5/80ad5989c6ea45f9.png',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/282486/30/4952/16417/67d94045F4b207f69/c97e5dff491a463b.png',
};

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  // 设置页面title
  document.title = activityData?.activityName || '全渠道新客礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  // app.provide('_decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
