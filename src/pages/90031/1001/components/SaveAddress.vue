<template>
  <div class="rule-bk">
    <div class="close" @click="close"></div>
    <div class="content">
      <div class="form">
        <div class="list">
            <div class="label"><text :key="index" v-for="(it, index) in '姓名'">{{it}}</text></div>
            <input class="input" v-model.trim="form.realName" maxlength="20" type="text" placeholder="收货人姓名">
        </div>
        <div class="list">
            <div class="label"><text :key="index" v-for="(it, index) in '电话'">{{it}}</text></div>
            <input class="input" v-model.trim="form.mobile" maxlength="11" type="text"
                    placeholder="收货人手机号">
        </div>
        <div class="list">
            <div class="label"><text :key="index" v-for="(it, index) in '地区'">{{it}}</text></div>
            <input class="input" v-model.trim="addressCode" readonly @click="addressSelects=true"
                    type="text" placeholder="选择 省/市/区">
        </div>
        <div class="list">
            <div class="label"><text :key="index" v-for="(it, index) in '详细地址'">{{it}}</text></div>
            <input class="input" v-model.trim="form.address" maxlength="35" type="text"
                    placeholder="街道门牌号">
        </div>
        <div class="list" v-if="needId === 1">
            <div class="label"><text :key="index" v-for="(it, index) in '身份证号'">{{it}}</text></div>
            <input class="input" v-model.trim="form.cId" maxlength="18" type="text"
                    placeholder="收货人身份证号">
        </div>
        <div style="width: 1.52rem;height: 0.54rem;" v-else>
        </div>
      </div>
      <div class="tip" v-if="needId === 1">根据相关要求，见面会等奖品登记需要实名制，请如实填写您的身份信息。我们将保护您的信息安全，且在必要确认时提供相关信息给关联方使用。</div>
      <div class="termCheckbox" v-if="needId === 1">
            <van-checkbox v-model="termCheckbox" checked-color="#33CCFF" icon-size="8px" :disabled="isFinished"></van-checkbox>
            <p>我已阅读并接受<span class="underline" @click="privacyPolicy = true">《隐私通知》</span>，同意提供相关个人信息</p>
          </div>
      <div class="submit" @click="checkForm"></div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
    <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="privacyPolicy" position="bottom">
    <div class="privacyPolicyBox">
      <div style="text-align: right;" @click="privacyPolicy=false">返回</div>
      <div style="text-align: center;font-size: 0.35rem;">隐私协议</div>
      <div style="width: 100%;height: 90vh; overflow: scroll;word-break: break-all;" v-html="furnish.privacyRule.replace(/\n/g, '<br/>')"></div>
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { showToast, closeToast, showLoadingToast, showDialog } from 'vant';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import { reactive, ref, computed, PropType, onMounted } from 'vue';
import { areaList } from '@vant/area-data';
import { FormType } from '../ts/type';
import { httpRequest } from '@/utils/service';
import { containsEmoji, containsSpecialChars, isPhoneNumber, isEmail, isPostCode, validateDataWithRules } from '../ts/validator';

const props = defineProps({
  addressId: {
    type: String,
    required: true,
  },
  activityPrizeId: {
    type: String,
    required: true,
  },
  echoData: {
    type: Object as PropType<FormType>,
    default: () => ({
      realName: '',
      mobile: '',
      province: '',
      city: '',
      county: '',
      address: '',
      cId: '',
    }),
  },
  needId: {
    type: Number,
    required: true,
  },
});

const emits = defineEmits(['close']);
const termCheckbox = ref(false);
const privacyPolicy = ref(false);
const close = () => {
  emits('close');
};

const addressSelects = ref(false);

const form: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
  cId: '',
});

onMounted(() => {
  // 回显地址
  Object.keys(form).forEach((key: string) => {
    form[key] = props.echoData[key];
  });
});

const addressCode = computed(() => {
  if (form.province && form.city && form.county) {
    return `${form.province}/${form.city}/${form.county}`;
  }
  return '';
});
const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
};
const ruleValidate = {
  realName: [
    {
      required: true,
      message: '请输入收货人',
    },
    {
      validator: containsSpecialChars,
      message: '收货人不能包含特殊字符',
    },
    {
      validator: containsEmoji,
      message: '收货人不能包含表情',
    },
  ],
  mobile: [
    {
      required: true,
      message: '请输入电话号码',
    },
    {
      validator: isPhoneNumber,
      message: '请输入正确的电话号码',
    },
  ],
  province: [
    {
      required: true,
      message: '请选择省/市/区',
    }],
  address: [
    {
      required: true,
      message: '请输入详细地址',
    },
    {
      validator: containsEmoji,
      message: '详细地址不能包含表情',
    },
  ],
};

const submit = async () => {
  const valid = validateDataWithRules(ruleValidate, form);
  if (!valid) return;
  if (props.needId === 1 && !form.cId) {
    showToast('请输入身份证号');
    return;
  }
  if (props.needId === 1 && !termCheckbox.value) {
    showToast('请您仔细阅读隐私通知');
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });

    const res = await httpRequest.post('/90031/userAddressInfo', {
      addressId: props.addressId,
      activityPrizeId: props.activityPrizeId,
      realName: form.realName,
      mobile: form.mobile,
      province: form.province,
      city: form.city,
      county: form.county,
      address: form.address,
      cId: form.cId,
    });
    closeToast();
    if (res.code === 200) {
      showDialog({
        className: 'dialog-white',
        title: '保存成功',
        message: '您的信息已提交成功',
      });
      emits('close', true);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 检查表单
const checkForm = () => {
  const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!form.realName) {
    showToast('请输入姓名');
  } else if (!form.mobile) {
    showToast('请输入电话');
  } else if (!phone.test(form.mobile)) {
    showToast('请输入正确的电话');
  } else if (!form.province) {
    showToast('请选择省市区');
  } else if (!form.address) {
    showToast('请输入详细地址');
  } else {
    submit();
  }
};
</script>

<style scoped lang="scss">
.privacyPolicyBox {
  width: 100vw;
  height: 100vh;
  background: #fff;
}
  .termCheckbox {
    margin-top: 0.1rem;
    font-size: 0.16rem;
    box-sizing: border-box;
    padding-left: 0.2rem;
    padding-right: 0.2rem;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 0.15rem;
    .van-checkbox {
      flex: none;
      width: 0.2rem;
      height: 0.2rem;
    }
    .underline {
      color: #33CCFF;
    }
  }
.WinningB_title {
        width: 100%;
        line-height: 0.7rem;
        text-align: center;
        font-size: .6rem;
        color: #ff7800;
        font-family: 'FZZZHONGHK';
        // margin-top: 1.2rem;
        font-weight: bold;
    }
.rule-bk {
  height: 6.9rem;
  width: 5.08rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/229027/12/21559/17045/66836765F3748980f/189d139d89a73759.png);
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: top;
  padding-top: 0.8rem;
  box-sizing: border-box;

  .title {
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.14rem;
    font-size: 0.34rem;
    color: #fff;

    .leftLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, left top, right top, from(#fff), to(#ff6153));
      background: linear-gradient(to right, #fff, #ff6153);
      border-radius: 4px;
      margin-right: 0.1rem;
    }

    .rightLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, right top, left top, from(#fff), to(#ff8c4a));
      background: linear-gradient(to left, #fff, #ff8c4a);
      border-radius: 4px;
      margin-left: 0.1rem;
    }
  }

  .close {
    height: 0.6rem;
    width: 0.6rem;
    position: absolute;
    bottom: 0;
    left: 2.24rem;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/232866/32/15925/1714/668253ddFa66fcc5b/cf1cbf3c1a60fbf1.png) no-repeat;
    background-size: 100% 100%;
  }
  .list {
    display: flex;
    align-items: center;
    font-size: 0.2rem;
    color: #fff;
    margin-bottom: .1rem;
    position: relative;
    justify-content: center;

    .label {
      flex: none;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/207093/10/38760/2167/66836a5fFf57086e7/dc39bb79a505159f.png');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: right;
      width: 1.52rem;
      height: 0.54rem;
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      padding-left: 0.2rem;
      padding-right: 0.2rem;
      font-size: 0.3rem;
      color: #ff7800;
      line-height: 0.54rem;
    }
    .input {
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/167304/26/42892/593/66837109Ffa5b534d/6b265042150b0a3e.png');
      background-position: left;
      background-repeat: no-repeat;
      background-size: contain;
      flex: none;
      width: 2.73rem;
      height: 0.54rem;
      outline: none;
      // border-radius: 0.1rem;
      // background-color: rgb(255, 255, 255);
      padding: .1rem 0;
      box-sizing: border-box;
      // margin-left: .1rem;
      border: 0;
      color: #000;
      text-align: center;
    }

    input::-webkit-input-placeholder {
      color: #999;
    }

    .icon {
      position: absolute;
      right: .3rem;
      transform: rotate(90deg);
    }
  }
  .content {
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    .form {
      .van-cell {
        border-radius: 0.08rem;
        margin-bottom: 0.1rem;
        background-color: #0083ff;

        &::after {
          display: none;
        }
      }
      .van-field__label {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/207093/10/38760/2167/66836a5fFf57086e7/dc39bb79a505159f.png');
        width: 1.52rem;
        height: 0.54rem;
      }
    }

    .tip {
      box-sizing: border-box;
      padding-left: 0.2rem;
      padding-right: 0.2rem;
      font-size: 0.14rem;
      color: #b3b3b3;
    }

    .submit {
      width: 100%;
      height: 0.63rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/226248/26/21521/8807/668367ceFfb1df659/4daba7c4dcae8ed4.png');
      background-repeat: no-repeat;
      background-size: contain;
      background-position: center;
    }
  }
}
</style>
