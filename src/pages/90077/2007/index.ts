import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init } from '@/utils';
import index from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin from '@/plugins/EventTracking';
import '@/style';
import './style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  backActRefresh: true,
  urlPattern: '/custom/:activityType/:templateCode',
};

init(config).then(({ baseInfo, pathParams, decoData }) => {
  // 设置页面title
  document.title = baseInfo?.activityName || '新单有礼';
  app.provide('baseInfo', baseInfo);
  app.provide('decoData', decoData);
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, { c: { level: baseInfo?.memberLevel || 0 } });
  app.mount('#app');
});
