import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const a = {
  actBg: '',
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/166924/33/49483/47044/67052e82Fc948f780/358c5f0e9eb52583.png',
  actBgColor: '#c0e3ff',
  shopNameColor: '#fff',
  ruleBg: '//img10.360buyimg.com/imgzone/jfs/t1/182827/22/49312/5829/67052e89F116813de/8146fcc15c71bb13.png',
  myPrizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/200071/7/45232/3533/67052e84Faed99043/ccb78ed4e6cd0188.png',
  equitySelection: '//img10.360buyimg.com/imgzone/jfs/t1/247598/16/20146/63235/67052e8aF3f02d3c8/78120d6c3120131e.png',
  qualificationBg: '//img10.360buyimg.com/imgzone/jfs/t1/111601/34/36464/21952/67052e80F9056a433/9af580c4ec770038.png',
  qualificationTextColor: '#1979d6',
  progressBarBg: '//img10.360buyimg.com/imgzone/jfs/t1/1443/23/26145/18303/67052e89Fba5aa931/b73fc85dec2a3850.png',
  topTextColor: '#eab434',
  bottomTextColor: '#1b79d5',
  orderLimitTextColor: '#c6dbf9',
  timeLimitedPrizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/93879/28/52762/59714/67062112F1fbf70c0/07e7854d26dd4d2d.png',
  timeLimitedTextColor: '#0069d0',
  timeLimitedBtnBg: '//img10.360buyimg.com/imgzone/jfs/t1/248033/22/20445/22719/67052e84F1ac68fc5/b33dc52022c9ada1.png',
  timeLimitedBtnTextColor: '#ffffff',
  prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/244374/2/20832/39571/67065479F55583a34/e0ee81b9d4cecbba.png',
  prizeItemBg: '//img10.360buyimg.com/imgzone/jfs/t1/167432/30/47772/8823/66f8ae43Fb0617e57/7a9267c696866378.png',
  prizeItemTitleColor: '#ae7116',
  getPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/184337/26/49263/6105/67052e89Fcac9ba65/2a2ca0f28ed6667b.png',
  showSkuBg: '//img10.360buyimg.com/imgzone/jfs/t1/97932/28/50157/1531062/67052e89F523e8ff4/088078ae93bc7576.png',
  priceColor: '#f6ce69',
  jumpUrl: '',
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/162797/1/47417/46510/67052e80F511a12a0/1119ca064f142b16.png',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/244185/5/19965/24164/67052e80F7db94c35/d12bcd2dd53794a4.png',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/248902/31/20523/48764/67052e80F656a482e/6f42063ab1daf9fd.png',
  canNotCloseJoinPopup: '1',
  hotZoneList: [
  ],
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '复购有礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  // app.provide('decoData', a);
  app.provide('isPreview', true);
  app.mount('#app');
});
