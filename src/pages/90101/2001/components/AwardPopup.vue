<template>
  <div class="bk" v-if="prize.prizeType !== 0" :style="furnishStyles.awardBk.value">
    <img :src="prize.showImg" alt="" class="prize-img" />
    <p class="prize-name" :style="furnishStyles.tipColor.value">{{ prize.prizeName }}</p>
    <div :style="furnishStyles.tipColor.value">
      <div :style="furnishStyles.tipColor.value" class="p3" v-if="prize.prizeType === 3">请在获得实物奖品的1h内填写完毕收货信息, <br />逾期未填写将视为用户主动放弃该奖品权益</div>
      <div :style="furnishStyles.tipColor.value" class="p3" v-else-if="prize.prizeType === 2">
        奖品将在确认收货后自动发放至您的账户，<br />
        届时可在<span :style="furnishStyles.tipImportColor.value">个人中心-京豆</span>中查看
      </div>
      <p :style="furnishStyles.tipColor.value" class="p3" v-else-if="prize.prizeType === 12">
        1:权益非自动发放,需首先填写权益领取信息 <br />
        2:如放弃领取权益,活动结束权益不予补发
      </p>
      <p :style="furnishStyles.tipColor.value" class="p3" v-else-if="prize.prizeType === 7">奖品将在确认收货后自动发放至我的奖品中，请在我的奖品中查看卡号或卡密</p>
      <div :style="furnishStyles.tipColor.value" class="p3" v-else>奖品将在确认收货后自动发放至您的账户</div>
    </div>
    <div class="btn">
      <img v-if="prize.prizeType === 3" :src="furnish.saveAddressBtn" alt="" @click="saveAddress" />
      <img v-else :src="furnish.confirmBtn" alt="" @click="close" />
    </div>
    <!-- <div class="close" @click="close"></div> -->
  </div>
  <div class="thanks-join" v-else :style="furnishStyles.notAwardBk.value">
    <!-- <div class="close" @click="close"></div> -->
    <div class="btn" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import { gotoShopPage } from '@/utils/platforms/jump';
import { callShare } from '@/utils/platforms/share';
import { PropType, inject } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { showToast } from 'vant';

const baseInfo = inject('baseInfo') as BaseInfo;
const isPreview = inject('isPreview') as boolean;

interface PrizeType {
  prizeType: number;
  prizeName: string;
  showImg: string;
  result: any;
  activityPrizeId: string;
  userPrizeId: string;
}
const props = defineProps({
  prize: {
    type: Object as PropType<PrizeType>,
    required: true,
  },
});

const emits = defineEmits(['close', 'saveAddress', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const saveAddress = () => {
  if (isPreview) {
    showToast('活动预览，仅供查看');
    return;
  }
  emits('saveAddress', props.prize.result.result, props.prize.userPrizeId);
};

const showCardNum = () => {
  emits('showCardNum', { ...props.prize.result, showImg: props.prize.showImg, prizeName: props.prize.prizeName });
};

const savePhone = () => {
  emits('savePhone', props.prize.userPrizeId, props.prize.result.result.planDesc);
};

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
</script>

<style scoped lang="scss">
.bk {
  width: 6.72rem;
  height: 5.8rem;
  background: url('../assets/award.png') no-repeat;
  background-size: 100%;
  padding: 1.35rem 0.4rem 0;
  .prize-img {
    height: 2rem;
    width: 2rem;
    margin: 0 auto;
  }
  .prize-name {
    font-size: 0.36rem;
    font-weight: bold;
    margin: 0.15rem 0 0;
    text-align: center;
    color: #fff;
  }
  .p3 {
    font-size: 0.22rem;
    color: #fff;
    display: block;
    text-align: center;
    height: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      color: #ffd800;
      display: contents;
    }
  }
  .btn {
    position: absolute;
    top: 5rem;
    left: 0;
    right: 0;
    img {
      height: 0.83rem;
      margin: 0 auto;
    }
  }
}
.thanks-join {
  width: 6.72rem;
  height: 5.7rem;
  background: url('../assets/notAward.png') no-repeat;
  background-size: 100%;
  padding-top: 4.1rem;
  .btn {
    display: block;
    margin: 0 auto;
    width: 4rem;
    height: 0.76rem;
  }
}
.close {
  position: absolute;
  bottom: 0;
  left: 2.95rem;
  right: 0.26rem;
  width: 0.77rem;
  height: 0.77rem;
}
</style>
