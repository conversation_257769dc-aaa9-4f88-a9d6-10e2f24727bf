<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img v-if="furnish.actBg" :src="furnish.actBg" alt="" class="kv-img" />
      <div class="hei" v-else></div>
      <div class="header-content">
        <div>
          <div class="header-btn">
            <img :src="furnish.ruleBtn" alt="" @click="showRulePopup()" />
            <img :src="furnish.myPrizeBtn" alt="" @click="showMyPrize = true" />
          </div>
        </div>
      </div>
    </div>
    <div>
      <div class="wheel" :style="furnishStyles.prizeContentBg.value">
        <div class="prize-list swiper-container">
          <div class="swiper-wrapper">
            <div class="swiper-slide" v-for="(item, index) in prizeInfo" :key="`${item.prizeName}${index}`">
              <div class="prize-item" :style="furnishStyles.prizeBg.value">
                <img :src="item.prizeImg" alt="" class="prize-img" />
                <div class="name">
                  <div :style="furnishStyles.prizeNameColor.value">{{ item.prizeName }}</div>
                </div>
                <div class="surplusNum" :style="furnishStyles.prizeNameColor.value">
                  <div v-if="item.surplusNum > 0">剩余{{ item.surplusNum }}份</div>
                  <div v-else>已抢光</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="draws-num" :style="furnishStyles.drawsNum.value">当前还有{{ chanceNum }}次抽奖机会</div>
      <img :src="furnish.drawBtn" class="draw-btn" alt="" @click="drawPrize" />
    </div>
    <div class="winners" :style="furnishStyles.winnersBg.value">
      <div class="winners-content">
        <div class="winner-list swiper-container" ref="swiperRef">
          <div class="swiper-wrapper" v-if="activityGiftRecords.length != 0">
            <div class="winner swiper-slide" v-for="(item, index) in activityGiftRecords" :key="index">
              <div>
                <img src="https://img10.360buyimg.com/imgzone/jfs/t20782/85/568972117/2758/78eafc28/5b0fd507Ne2b074cc.png" alt="" v-if="!item.userImg" />
                <img v-else :src="item.userImg" alt="" />
                <span>{{ item.nickName }}</span>
              </div>
              <span>{{ item.prizeName }}</span>
            </div>
          </div>
          <div v-else>
            <p class="winner-null">暂无相关获奖信息哦~</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize">
    <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="showAward">
    <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></AwardPopup>
  </VanPopup>
  <!-- 保存地址弹窗 -->
  <VanPopup teleport="body" v-model:show="showSaveAddress">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" @close="showSaveAddress = false"></SaveAddress>
  </VanPopup>
  <!-- 展示卡密 -->
  <VanPopup teleport="body" v-model:show="copyCardPopup">
    <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
  </VanPopup>
  <!-- 抽奖次数不足 -->
  <VanPopup v-model:show="noChanceNum">
    <NoChanceNum :oderPrice="orderRestrainAmount" @close="noChanceNum = false"></NoChanceNum>
  </VanPopup>
  <!-- 去开卡 -->
  <VanPopup v-model:show="isJoin">
    <JoinPop @close="isJoin = false"></JoinPop>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, inject } from 'vue';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import Swiper, { Autoplay } from 'swiper';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { CardType } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import SavePhone from '../components/SavePhone.vue';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import Threshold2 from '@/components/Threshold2/index.vue';
import useThreshold from '@/hooks/useThreshold';
import GoodsPopup from '../components/GoodsPopup.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import 'swiper/swiper.min.css';
import dayjs from 'dayjs';
import DrawRecordPopup from '../components/DrawRecordPopup.vue';
import NoChanceNum from '../components/NoChanceNum.vue';
import JoinPop from '../components/JoinPop.vue';

Swiper.use([Autoplay]);

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
// 0-全部商品 1-指定商品  2-排除
const orderSkuisExposure = ref(0);
const shopName = ref(baseInfo.shopName);

const showRule = ref(false);
const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error: any) {
    console.error();
  }
};

// 订单门槛
const orderRestrainAmount = ref('');
// 抽奖次数
const chanceNum = ref(0);
// 参与人数
const joinNum = ref(0);
// 订单状态
const orderRestrainStatus = ref(0);
// 是否入会
const isJoin = ref(false);
// 抽奖次数不足
const noChanceNum = ref(false);
const showMyPrize = ref(false);

const showGoods = ref(false);
const showOrderRecord = ref(false);
type Sku = {
  skuName: string;
  skuMainPicture: string;
  skuId: number;
  jdPrice: string;
};

const skuList = ref<Sku[]>([]);
const pageNum = ref(1);
const pagesAll = ref(0);

const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});
const showDrawRecord = ref(false);
// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  showAward.value = false;
  showSaveAddress.value = true;
};

const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    event: () => {
      showRulePopup();
    },
  },
  {
    name: '我的奖品',
    event: () => {
      showMyPrize.value = true;
    },
  },
  {
    name: '活动商品',
    event: () => {
      showGoods.value = true;
    },
  },
  {
    name: '我的订单',
    event: () => {
      showOrderRecord.value = true;
    },
  },
  {
    name: '抽奖记录',
    event: () => {
      showDrawRecord.value = true;
    },
  },
];

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showAward.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  showAward.value = false;
  showMyPrize.value = false;
  showGoods.value = false;
  showOrderRecord.value = false;
  savePhonePopup.value = true;
};

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}

const activityGiftRecords = reactive([] as ActivityGiftRecord[]);

// 不在活动范围呢刷新页面
const unStart = (partakeStartTime: string) => {
  const now = dayjs().format('YYYY-MM-DD');
  let time = 0;
  if (partakeStartTime > dayjs().format('HH:mm:ss')) {
    time = dayjs(`${now} ${partakeStartTime}`).valueOf() - dayjs().valueOf();
  } else {
    time = dayjs(`${now} ${partakeStartTime}`).add(1, 'day').valueOf() - dayjs().valueOf();
  }
  setTimeout(() => {
    window.location.reload();
  }, time);
};

// 判断用户是否入会 是否有抽奖资格
const isJoinOrHasChance = (list: any) => {
  const threshold = list.find((item: any) => item.thresholdCode === 4);
  if (threshold) {
    isJoin.value = true;
    return false;
  }
  const threshold2 = list.find((item: any) => item.thresholdCode === 204);
  if (threshold2) {
    noChanceNum.value = true;
    return false;
  }
  return true;
};

// 判断当前时间是否在活动时间内
const isActivityTime = () => {
  console.log('baseInfo333333333333', baseInfo);
  if (baseInfo.status === 1) {
    showToast({
      message: '活动未开始',
      forbidClick: true,
    });
    return false;
  }
  if (baseInfo.status === 2) {
    return isJoinOrHasChance(baseInfo.thresholdResponseList);
  }
  if (baseInfo.status === 3) {
    console.log(3444444444);
    showToast({
      message: '活动已结束',
      forbidClick: true,
      duration: 3000,
    });
    return false;
  }
  return true;
};

// 获取客抽奖次数
const getChanceNum = async () => {
  try {
    const { data } = await httpRequest.post('/90101/chanceNum');
    chanceNum.value = data.chanceNum;
    joinNum.value = data.joinNum;
    orderRestrainAmount.value = data.orderRestrainAmount;
    orderRestrainStatus.value = data.orderRestrainStatus;
    orderSkuisExposure.value = data.orderSkuisExposure;
    if (baseInfo.thresholdResponseList.length) {
      const threshold = baseInfo.thresholdResponseList.find((item: any) => item.thresholdCode === 201);
      if (threshold) {
        unStart(data.partakeStartTime);
      }
    }
  } catch (error: any) {
    console.error(error);
  }
};

let prizeSwiper: Swiper;

// 获取奖品信息
const getPrizes = async () => {
  try {
    const { data } = await httpRequest.post('/90101/getPrizes');
    const _data = data.filter((item: any) => item.prizeName !== '谢谢参与');
    prizeInfo.splice(0);
    prizeInfo.push(..._data);
    nextTick(() => {
      if (prizeSwiper) prizeSwiper.destroy();
      prizeSwiper = new Swiper('.prize-list', {
        autoplay: prizeInfo.length >= 3 ? { delay: 1000, stopOnLastSlide: false, disableOnInteraction: false } : false,
        loop: prizeInfo.length >= 3,
        slidesPerView: 3,
        loopedSlides: 5,
        centeredSlides: true,
        observeSlideChildren: true,
        observer: true,
      });
    });
  } catch (error: any) {
    console.error(error);
  }
};

// 抽奖接口
const lotteryDraw = async () => {
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  try {
    const res = await httpRequest.post('/90101/lotteryDraw');
    if (res.data.prizeType) {
      award.value = {
        prizeType: res.data.prizeType,
        prizeName: res.data.prizeName,
        showImg: res.data.prizeImg,
        result: res.data.result ?? '',
        activityPrizeId: res.data.activityPrizeId ?? '',
        userPrizeId: res.data.userPrizeId,
      };
    } else {
      award.value = {
        prizeType: 0,
        prizeName: '谢谢参与',
        showImg: '',
        result: '',
        activityPrizeId: '',
        userPrizeId: '',
      };
    }
  } catch (error: any) {
    award.value = {
      prizeType: 0,
      prizeName: '谢谢参与',
      showImg: '',
      result: '',
      activityPrizeId: '',
      userPrizeId: '',
    };
    console.error(error);
  }
  closeToast();
  showAward.value = true;
  getChanceNum();
  getPrizes();
};
const drawPrize = () => {
  lzReportClick('kscj');
  if (!isActivityTime()) {
    return;
  }
  if (chanceNum.value <= 0) {
    // showToast('您的抽奖次数已用完');
    noChanceNum.value = true;
    return;
  }
  lotteryDraw();
};
// 抽奖结束会触发end回调
const endCallback = (prize: any) => {
  showAward.value = true;
};

// 获取中奖名单
const getWinners = async () => {
  try {
    const res = await httpRequest.post('/90101/winners');
    activityGiftRecords.splice(0);
    activityGiftRecords.push(...res.data);
    nextTick(() => {
      nextTick(() => {
        const mySwiper = new Swiper('.winner-list', {
          autoplay: activityGiftRecords.length > 4 ? { delay: 1000, stopOnLastSlide: false, disableOnInteraction: false } : false,
          direction: 'vertical',
          loop: activityGiftRecords.length > 4,
          slidesPerView: 4,
          loopedSlides: 6,
          spaceBetween: 5,
        });
      });
    });
  } catch (error: any) {
    console.error(error);
  }
};
// 获取曝光商品
const getSkuList = async () => {
  try {
    const res = await httpRequest.post('/90101/getExposureSkuPage', {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 10,
    });
    if (res.code === 200) {
      skuList.value.push(...res.data.records);
      pagesAll.value = res.data.pages;
    }
  } catch (error: any) {
    console.error(error);
  }
};
const loadMore = async () => {
  pageNum.value++;
  await getSkuList();
};
const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }

  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });

    await Promise.all([getChanceNum(), getPrizes(), getWinners(), getSkuList()]);
    closeToast();
    isActivityTime();
  } catch (error: any) {
    closeToast();
  }
  if (baseInfo.status === 1) {
    const time = baseInfo.startTime - dayjs().valueOf();
    setTimeout(() => {
      window.location.reload();
    }, time);
  }
};
init();
</script>

<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.2rem;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }
  .hei {
    height: 5.12rem;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 1.62rem;
    img {
      width: 1.71rem;
    }
  }
}

.wheel {
  background-size: 100%;
  background-repeat: no-repeat;
  width: 7.5rem;
  height: 3.4rem;
  position: relative;
  padding-top: 0.16rem;
  overflow: hidden;
  .prize-list {
    width: 7.3rem;
    margin: 0 auto;
  }
  .prize-item {
    width: 2.39rem;
    height: 3.05rem;
    background-size: 100%;
    background-repeat: no-repeat;
    padding-top: 0.2rem;
    transform: scale(0.87);
    transition: 200ms;
    .prize-img {
      width: 2rem;
      height: 1.85rem;
      border-radius: 0.2rem;
      margin: 0 auto;
      object-fit: cover;
    }
    .name {
      font-size: 0.23rem;
      line-height: 0.25rem;
      text-align: center;
      word-wrap: break-word;
      display: flex;
      align-items: center;
      height: 0.5rem;
      margin-top: 0.15rem;
      padding: 0 0.1rem;
      font-weight: bold;
      div {
        width: 100%;
        // 文字最多两行
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
    .surplusNum {
      text-align: center;
      font-size: 0.18rem;
      line-height: 0.18rem;
    }
  }
  .swiper-slide-active .prize-item {
    transform: scale(1);
  }
}

.draws-num {
  text-align: center;
  font-size: 0.41rem;
  font-weight: bold;
  margin-top: 0.4rem;
  margin-bottom: 0.15rem;
}

.draw-btn {
  width: 2.82rem;
  margin: 0 auto;
}

.winners {
  background-size: 100%;
  background-repeat: no-repeat;
  width: 7.13rem;
  height: 4.62rem;
  margin: 0.3rem auto 0;
  padding-top: 0.76rem;

  .winners-content {
    width: 6rem;
    height: 3.34rem;
    //background-color: #fff;
    border-radius: 0.1rem;
    margin: 0 auto;
  }
}

.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
  //padding: 0 0.3rem;
}

.winner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.2rem;
  background: #ffffff;
  border-radius: 0.1rem;

  img {
    width: 0.6rem;
    height: 0.6rem;
    object-fit: cover;
    border-radius: 1.2rem;
    display: inline;
    vertical-align: middle;
    margin-right: 0.1rem;
  }

  span {
    vertical-align: middle;
    font-size: 0.28rem;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.winner-null {
  text-align: center;
  line-height: 3.3rem;
  font-size: 0.24rem;
  color: #8c8c8c;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
