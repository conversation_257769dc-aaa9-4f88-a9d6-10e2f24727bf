import { computed, reactive } from 'vue';

export const taskRequestInfo = reactive([
  {
    skuId: '',
    gradeValue: 0,
    id: '',
    level: '',
    gradeValueName: '',
    position: 1,
    giftSkuList: [],
    taskRule: '',
    sendTotalCount1: 1,
    joinSkuType: 1, // 1全店商品 2指定商品
    skuList: [],
    prizeList: [], // 根据实际情况，可能需要定义奖品的类型
  },
]);
export const furnish = reactive<{ [x: string]: string }>({
  pageBg: '',
  actBg: '', // 主页背景图
  actBgColor: '', // 主页背景色
  ruleBtn: '', // 活动规则按钮
  myPrizeBtn: '', // 我的奖品按钮
  orderBtn: '', // 我的订单按钮
  seriesBg: '', // 系列背景图
  userInfoBg: '', // 用户信息背景图
  thresholdBg: '', // 门槛背景图
  seriesTitleColor: '', // 系列标题颜色
  bugAmountColor: '', // 购买金额颜色
  stepBtnBg: '', // 阶梯按钮
  stepBtnSelectBg: '', // 阶梯按钮选中
  stepTextColor: '', // 阶梯文字颜色
  stepTextColorShadow: '', // 阶梯文字阴影颜色
  prizeListBg: '', // 奖品列表背景图
  prizeBg: '', // 奖品背景图
  exchangeBtn: '', // 兑换按钮
  skuListBg: '', // 商品列表背景图
  skuBg: '', // 商品背景图
  skuTitleBg: '', // 商品标题背景图
  skuTitleColor: '', // 商品标题颜色
  goSkuBtn: '', // 去商品按钮
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const seriesBg = computed(() => ({
  backgroundImage: furnish.seriesBg ? `url("${furnish.seriesBg}")` : '',
}));

const seriesTitleColor = computed(() => ({
  color: furnish.seriesTitleColor ?? '',
}));

const bugAmountColor = computed(() => ({
  color: furnish.bugAmountColor ?? '',
}));

const stepBtnBg = computed(() => ({
  backgroundImage: furnish.stepBtnBg ? `url("${furnish.stepBtnBg}")` : '',
  color: furnish.stepTextColor ?? '',
}));

const stepBtnSelectBg = computed(() => ({
  backgroundImage: furnish.stepBtnSelectBg ? `url("${furnish.stepBtnSelectBg}")` : '',
  color: furnish.stepTextColor ?? '',
}));

const stepText = computed(() => ({
  fill: furnish.stepTextColor ?? '',
  stroke: furnish.stepTextColorShadow ?? '',
  strokeWidth: '0.06rem',
  paintOrder: 'stroke',
}));

const prizeBg = computed(() => ({
  backgroundImage: furnish.prizeBg ? `url("${furnish.prizeBg}")` : '',
}));

const skuListBg = computed(() => ({
  backgroundImage: furnish.skuListBg ? `url("${furnish.skuListBg}")` : '',
}));

const skuBg = computed(() => ({
  backgroundImage: furnish.skuBg ? `url("${furnish.skuBg}")` : '',
}));

const skuTitleBg = computed(() => ({
  backgroundImage: furnish.skuTitleBg ? `url("${furnish.skuTitleBg}")` : '',
  color: furnish.skuTitleColor ?? '',
}));

const userInfoBg = computed(() => ({
  backgroundImage: furnish.userInfoBg ? `url("${furnish.userInfoBg}")` : '',
  color: furnish.userInfoColor ?? '',
}));

const thresholdBg = computed(() => ({
  backgroundImage: furnish.thresholdBg ? `url("${furnish.thresholdBg}")` : '',
}));

const prizeListBg = computed(() => ({
  backgroundImage: furnish.prizeListBg ? `url("${furnish.prizeListBg}")` : '',
}));
export default {
  pageBg,
  seriesBg,
  seriesTitleColor,
  bugAmountColor,
  stepBtnBg,
  stepBtnSelectBg,
  stepText,
  prizeBg,
  skuListBg,
  skuBg,
  skuTitleBg,
  userInfoBg,
  thresholdBg,
  prizeListBg,
};
