<template>
    <div class="vip-bg">
        <div class="joinVipClass">
          <div class="joinVipDiv" @click="joinClick()"></div>
        </div>
      <div class="closeDiv" @click="close()"></div>
    </div>
</template>
<script setup lang="ts">
import { defineEmits, defineProps, inject, ref, watch } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo: any = inject('baseInfo') as BaseInfo;
const props = defineProps(['data', 'show']);
const emits = defineEmits(['update:show', 'close']);

// 立即入会
// 立即入会
const joinClick = () => {
  const openCardLinkNew = 'https://shopmember.m.jd.com/shopcard?venderId=1000004489&shopId=1000004489&venderType=1&channel=8017015';
  console.log('立即入会', baseInfo.openCardLink, openCardLinkNew);
  window.location.href = `${openCardLinkNew}&returnUrl=${encodeURIComponent(window.location.href)}`;
};
const close = () => {
  emits('close');
};

</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
<style lang="scss" scoped>
.vip-bg{
    display:flex;
    flex-direction: column;
    align-items: center;
   .joinVipClass{
     background: url(//img10.360buyimg.com/imgzone/jfs/t1/117403/28/49210/65242/6711cc28F2e15149b/526ca9370232a925.png) no-repeat;
     background-size: 100%;
     width: 5.99rem;
     height: 7.54rem;
     padding-top: 1.24rem;
     display: flex;
     justify-content: center;
     position:relative;
     .joinVipDiv{
       position: absolute;
       bottom: 1.74rem;
       //background:red;
       height:0.87rem;
       width: 4.18rem;
       left: calc(50% - 4.18rem / 2);
     }
   }
   .closeDiv{
     position: absolute;
     bottom: 0;
     //background:red;
     height:0.6rem;
     width: 0.6rem;
     left: calc(50% - 0.6rem / 2);
   }
}
</style>
