<template>
  <div class="rule-bk">
    <div class="content">
      <div class="form">
        <div class="inputDivAll">
          <div class="titleDiv">&nbsp;&nbsp;&nbsp;&nbsp;收货人</div>
          <div class="inputDiv">
            <input :readonly="!!addressData.realName" v-model="form.realName" maxlength="20" placeholder="请输入收货人" />
          </div>
        </div>
        <div class="inputDivAll">
          <div class="titleDiv">&nbsp;&nbsp;&nbsp;&nbsp;手机号</div>
          <div class="inputDiv">
            <input :readonly="!!addressData.mobile" v-model="form.mobile" maxlength="11" placeholder="请输入手机号" />
          </div>
        </div>
        <div class="inputDivAll">
          <div class="titleDiv">所在地区</div>
          <div class="inputDiv">
            <input v-model="addressCode" readonly @click="addressSelectsClick()" placeholder="请选择所在地区"/>
          </div>
        </div>
        <div class="inputDivAll">
          <div class="titleDiv">详细地址</div>
          <div class="inputDiv">
            <input :readonly="!!addressData.address" v-model="form.address" placeholder="请输入详细地址" />
          </div>
        </div>
      </div>
<!--      <div class="tip">请注意：地址填写简略、手机号填写错误皆会影响派单，导致您无法收到商品！（超过1小时未填写收货地址信息，视为放弃）</div>-->
      <div class="submitGray" v-if="addressData.realName">提交</div>
      <div class="submit" v-else @click="checkForm">提交</div>
    </div>
    <div class="closeDiv" @click="close()"></div>
  </div>

  <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
    <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
  </VanPopup>
</template>

<script lang="ts" setup>
import { showToast, closeToast, showLoadingToast } from 'vant';
import { reactive, ref, computed, PropType, onMounted, inject } from 'vue';
import { areaList } from '@vant/area-data';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
interface FormType {
  realName: string;
  mobile: string;
  province: string;
  city: string;
  county: string;
  address: string;
  addressId: string;
  activityPrizeId: string;
  userPrizeId: string;
}
const props = defineProps({

  addressData: {
    type: Object as PropType<FormType>,
    default: () => ({
      realName: '',
      mobile: '',
      province: '',
      city: '',
      county: '',
      address: '',
    }),
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
const addressSelects = ref(false);
const addressSelectsClick = () => {
  if (props.addressData.realName) {
    return;
  }
  addressSelects.value = true;
};
const form: FormType = reactive({
  addressId: '',
  activityPrizeId: '',
  userPrizeId: '',
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

onMounted(() => {
  // 回显地址
  Object.keys(form).forEach((key: string) => {
    form[key] = props.addressData[key];
  });
});

const addressCode = computed(() => {
  if (form.province && form.city && form.county) {
    return `${form.province}/${form.city}/${form.county}`;
  }
  return '';
});
// const addressCode = ref('');

const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
};

const submit = async () => {
  if (baseInfo.status === 3) {
    showToast('活动已结束');
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/91003/userAddressInfo', {
      realName: form.realName,
      mobile: form.mobile,
      province: form.province,
      city: form.city,
      county: form.county,
      address: form.address,
    });
    closeToast();
    if (res.code === 200) {
      showToast('保存成功');
      emits('close', true);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 检查表单
const checkForm = () => {
  const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!form.realName) {
    showToast('请输入收货人');
  } else if (!form.mobile) {
    showToast('请输入手机号');
  } else if (!phone.test(form.mobile)) {
    showToast('请输入正确格式的手机号');
  } else if (!form.province) {
    showToast('请选择所在地区');
  } else if (!form.address) {
    showToast('请输入详细地址');
  } else {
    submit();
  }
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/193324/25/49015/53360/6711cc27F0c742c21/80b3ec397754b48b.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  height: 7.64rem;
  width:6.01rem;
  padding-top:1.10rem;
  .closeDiv {
    position: absolute;
    bottom: 0;
    //background:red;
    height:0.6rem;
    width: 0.6rem;
    left: calc(50% - 0.6rem / 2);
  }

  .content {
    //border: 0.3rem solid transparent;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    margin-top: 0.3rem;
    .form {
      .inputDivAll{
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom:0.32rem;

        .titleDiv{
          color:#f7e3cf;
          font-size:0.27rem;
          margin-right: 0.17rem;
          text-align: right;
          white-space: pre;
        }
        .inputDiv{
          input{
            border: 0.02rem solid #574d81;
            background: #2b1e5c;
            box-sizing: border-box;
            height: 0.48rem;
            width: 3.68rem;
            padding-left: 0.24rem;
            color:#9e99b3;
          }
        }
      }
    }

    .tip {
      font-size: 0.18rem;
      color: #b3b3b3;
    }

    .submit {
      margin-top: 0.1rem;
      text-align: center;
      font-size: 0rem;
      line-height: 0.87rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/96121/23/49937/3179/6711cc27F6f66dcb2/7db8b6893b9d98b1.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      height: 0.87rem;
      width:4.18rem;
      margin-left: calc(50% - 4.18rem / 2);
    }
    .submitGray{
      margin-top: 0.1rem;
      text-align: center;
      font-size: 0;
      line-height: 0.87rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/170618/19/52059/6921/671b3f23F9168a3d2/2fea77f5bd300243.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      height: 0.87rem;
      width:4.18rem;
      margin-left: calc(50% - 4.18rem / 2);
      filter: grayscale(1);
    }
  }
}
</style>
