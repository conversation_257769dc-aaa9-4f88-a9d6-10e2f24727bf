<template>
  <div class="rule-bk">
    <div v-if="popType === 1" class="tipDiv1" :style="{'backgroundImage': 'url(' + furnish.submitTipPopBg + ')' }">
      <div class="btnDiv1">
        <div class="btnBack" @click="close">返回修改</div>
        <div class="submitBtn" @click="sureSubmitInfoClick()">确认提交</div>
      </div>
    </div>
    <div v-if="popType === 2" class="tipDiv2" :style="{'backgroundImage': 'url(' + furnish.otherPopBg + ')' }">
      <div class="errMessageDiv">{{errMessage}}</div>
      <div class="btnDiv2">
        <div class="knowBtn" @click="close">知道了</div>
      </div>
    </div>
    <div v-if="popType === 3" class="tipDiv3" :style="{'backgroundImage': 'url(' + furnish.reviewSuccPopBg + ')' }">
      <div class="btnDiv3">
        <div class="knowBtn" @click="goToBuyClick()">去下单</div>
      </div>
    </div>
    <div v-if="popType === 5" class="tipDiv5" :style="{'backgroundImage': 'url(' + furnish.submitFailPopBg ? furnish.submitFailPopBg : '//img10.360buyimg.com/imgzone/jfs/t1/250045/38/38146/45055/681b03e1F7336a244/3b44ac6deb3c2563.png' + ')' }">
      <div class="btnDiv3">
        <div class="knowBtn" @click="goToBuyClick()">进店逛逛</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject } from 'vue';
import { gotoShopPage } from '@/utils/platforms/jump';
import { BaseInfo } from '@/types/BaseInfo';
import { furnish } from '../ts/furnishStyles';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  // 1 提交注意  2 审核进度(审核中/未通过)  3审核通过 5 不符合老客身份
  popType: {
    type: Number,
    default: 0,
  },
  errMessage: {
    type: String,
    default: '',
  }
});

const emits = defineEmits(['close', 'sureSubmitInfo']);

const close = () => {
  emits('close');
};
// 确认提交
const sureSubmitInfoClick = () => {
  emits('sureSubmitInfo');
};
// 去下单
const goToBuyClick = () => {
  console.log("去下单");
  gotoShopPage(baseInfo.shopId);
};
</script>

<style scoped lang="scss">
.rule-bk {
  .tipDiv1{
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/274045/20/28740/82520/6810d1f1Fc58bbee0/3503edd5e1086f5c.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 4.79rem;
    height: 3.91rem;
    position: relative;
    .btnDiv1{
      display: flex;
      justify-content: center;
      position: absolute;
      bottom: 0.1rem;
      font-size: 0;
      width: 4.2rem;
      left: calc(50% - 4.2rem / 2);
      .btnBack{
        width: 2.5rem;
        height: 0.6rem;
        //background-color: black;
      }
      .submitBtn{
        width: 2.5rem;
        height: 0.6rem;
        //background-color: black;
      }
    }
  }
  .tipDiv2{
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/296359/29/1221/12765/68109c08Fb907196f/1ae95bad24198616.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 4.79rem;
    height: 3.91rem;
    position: relative;
    padding-top: 0.83rem;
    .errMessageDiv{
      font-size: 0.3rem;
      font-weight: bold;
      color: #e70404;
      text-align: center;
      width: 3.3rem;
      margin-left: 50%;
      transform: translateX(-50%);
    }
    .btnDiv2{
      display: flex;
      justify-content: center;
      position: absolute;
      bottom: 0.36rem;
      font-size: 0;
      width: 100%;
      .knowBtn{
        //background-color: black;
        width: 2.73rem;
        height: 0.74rem;
      }
    }
  }
  .tipDiv3{
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/288599/8/1261/47959/6811acfdF80ce0c0d/071dd21ff1170050.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 4.79rem;
    height: 3.91rem;
    position: relative;
    .btnDiv3{
      display: flex;
      justify-content: center;
      position: absolute;
      bottom: 0.36rem;
      font-size: 0;
      width: 100%;
      .knowBtn{
        //background-color: black;
        width: 2.73rem;
        height: 0.74rem;
      }
    }
  }
  .tipDiv5{
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/288570/29/4985/46511/681d71eeFfdc3a35e/fd16b2f635409a79.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 4.79rem;
    height: 3.91rem;
    position: relative;
    .btnDiv3{
      display: flex;
      justify-content: center;
      position: absolute;
      bottom: 0.36rem;
      font-size: 0;
      width: 100%;
      .knowBtn{
        //background-color: black;
        width: 2.73rem;
        height: 0.74rem;
      }
    }
  }
}
</style>
