<template>
  <div class="rule-bk">
    <div class="tip" v-if="notReceiveCode === '0'" style="font-size: 0.4rem">
      <div>您暂不符合新客资格</div>
    </div>
    <div class="tip" v-else-if="notReceiveCode === 'F0'">
      <div>您填写的宝宝月龄不符合此权益</div>
      <div>领取资格当前权益:1段适用月龄0-6个月</div>
    </div>
    <div class="tip" v-else-if="notReceiveCode === 'F2-N'">
      <div>您填写的宝宝月龄不符合此权益</div>
      <div>领取资格当前权益:2段适用月龄6月-10月</div>
    </div>
    <div class="tip" v-else-if="notReceiveCode === 'F3'">
      <div>您填写的宝宝月龄不符合此权益</div>
      <div>领取资格当前权益:3段适用月龄10月-36月</div>
    </div>
    <div class="tip" v-else style="font-size: 0.4rem">
      <div>您填写的宝宝月龄不符合此权益</div>
    </div>
    <div class="show-rule-btn" @click="emits('showRule')"></div>
    <div class="to-shop-btn" @click="gotoShopPage(baseInfo.shopId)"></div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script setup lang="ts">
import { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage } from '@/utils/platforms/jump';
import { inject } from 'vue';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

const props = defineProps(['notReceiveCode']);
const emits = defineEmits(['close', 'showRule']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-repeat: no-repeat;
  background-image: url('../assets/notReceive.png');
  background-size: 100%;
  width: 6.15rem;
  height: 5.25rem;
  padding: 1.8rem 0 0 0;
  position: relative;
  .tip {
    height: 1.8rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    color: #0a4aa8;
    font-size: 0.31rem;
    line-height: 0.45rem;
  }
  .show-rule-btn {
    width: 2.51rem;
    height: 0.76rem;
    position: absolute;
    top: 3.88rem;
    left: 0.37rem;
  }
  .to-shop-btn {
    width: 2.51rem;
    height: 0.76rem;
    position: absolute;
    top: 3.88rem;
    right: 0.37rem;
  }
  .close {
    width: 0.41rem;
    height: 0.42rem;
    position: absolute;
    top: 0.25rem;
    right: 0.31rem;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
</style>
