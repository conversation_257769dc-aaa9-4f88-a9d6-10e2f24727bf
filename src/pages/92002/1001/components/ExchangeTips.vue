<template>
  <div class='confirm-box'>
    <div class='info-box'>
      {{ exchangeTips }}
    </div>
    <div class='btn-box'>
      <div class='exchange-btn' @click="confirm">{{ btnName }}</div>
    </div>
  </div>
  <div class='close-btn' @click="close" />
</template>
<script lang="ts" setup>
const props = defineProps({
  exchangeTips: {
    type: String,
    default: '""',
  },
  btnName: {
    type: String,
    default: '""',
  },
});
const emits = defineEmits(['close', 'confirm']);
const close = () => {
  emits('close');
};
const confirm = () => {
  emits('confirm');
};
</script>
<style scoped lang="scss">
.confirm-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 5rem;
  height: 3.5rem;
  background-color: #fff;
  border-radius: 0.2rem;
  .info-box {
    margin-bottom: 0.86rem;
    width: 3.38rem;
    font-family: PingFang-SC-Regular;
    font-size: 0.36rem;
    color: #333333;
    text-align: center;
  }
  .num-box {
    margin: 0rem 0.05rem;
    font-family: PingFang-SC-Regular;
    font-size: 0.36rem;
    color: #ff3333;
  }
  .btn-box {
    position: absolute;
    left: 0;
    top: 2.65rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 5rem;
    height: 0.86rem;
    cursor: pointer;
    .exchange-btn {
      width: 5rem;
      height: 0.86rem;
      line-height: 0.86rem;
      text-align: center;
      font-family: PingFang-SC-Regular;
      font-size: 0.3rem;
      background-color: #f65f12;
      color: #fff;
      border-radius: 0rem 0rem 0.2rem 0.2rem;
    }
  }
}
.close-btn {
  margin: 0.75rem 0rem 0rem 2.23rem;
  width: 0.6rem;
  height: 0.6rem;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/110430/17/39957/1262/6459a770Fd709c84b/d608c15c4f784ed7.png");
    repeat: no-repeat;
    size: contain;
  }
  cursor: pointer;
}
</style>
