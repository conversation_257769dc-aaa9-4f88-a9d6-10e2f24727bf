import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init, initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const _decoData = {
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/280758/10/21753/367612/68076644F707d70a7/a74fc8cd7c4fa8c9.png',
  actBgColor: '#e6e4e9',
  ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/279162/31/18263/1717/67f7af74F616737c9/aace214116988f93.png',
  myPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/271135/32/21242/1766/67fda59cFe0e57d05/51a21b2a7ecbcb32.png',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
};

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '会员生日礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
