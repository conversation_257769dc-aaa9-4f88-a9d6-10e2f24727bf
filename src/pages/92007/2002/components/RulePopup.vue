<template>
  <div class="rule-bk">
    <div class="rule">
      <div v-html="rule"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { ref } from 'vue';

const rule = ref('');

const getRule = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    closeToast();
    const res = await httpRequest.get('/common/getRule');
    rule.value = res.data;
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};
getRule();
</script>

<style scoped lang="scss">
.rule-bk {
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/237078/15/18169/14572/6655469fFc272faa3/e2f8059d6874a399.png') no-repeat;
  background-size: 100%;
  width: 5.8rem;
  height: 7.16rem;
  padding-top: 1.6rem;
  padding-bottom: 0.3rem;
}
.rule {
  height: 100%;
  padding: 0 0.35rem;
  font-size: 0.24rem;
  color: #9c693e;
  text-shadow: 0 0 1px #ffffff, /* 水平偏移，垂直偏移，模糊半径，阴影颜色 */
  0 0 1px #ffffff,
  0 0 1px #ffffff,
  0 0 10px #ffffff;
  white-space: pre-wrap;
  word-break: break-all;
  div {
    height: 100%;
    overflow-y: scroll;
  }
}
</style>
