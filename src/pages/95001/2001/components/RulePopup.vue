<template>
  <div class="rule-bk">
    <div class="title">
      <div class="leftLineDiv"></div>
      <div class="title-rotate">活动规则</div>
      <div class="rightLineDiv"></div>
      <img alt="" data-v-705393a4="" src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png" class="close" @click="close" />
    </div>
    <div class="content" v-html="rule"></div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-color: #e1e1e1;
  border-radius: 0.2rem 0.2rem 0 0;
  width: 100vw;
  height: 7rem;

  .title {
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/240907/8/14115/16362/66866887F07e813a8/939f820eb4abf39a.png);
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    height: 1.9rem;
    line-height: 2rem;
    font-size: 0.6rem;
    color: #815b36;
    .title-rotate {
      transform: rotate(-5deg);
    }

  }

  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.22rem;
  }

  .content {
   height: 4.5rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    background-color: #fff;
    margin: 0.2rem 0.2rem 0 0.2rem;
    border-radius: 0.2rem ;
    padding:0.1rem;
    word-break: break-word;
  }
}
</style>
