<template>
  <div class="rule-bk">
    <div class="title">
    </div>
    <div class="content" v-html="rule"></div>
    <div  class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  border-radius: 0.2rem 0.2rem 0 0;
  width: 6.8rem;
  height: 8rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/249318/37/15256/16688/669628a6F7b820764/da5b9839b1cf3745.png);
  background-size: 100%;
  background-repeat: no-repeat;

  .title {
    height: 1.18rem;
  }

  .close {
    height: 1rem;
    width: 1rem;
    margin: 0 auto;
  }

  .content {
    height: 5rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    background-color: #fff;
    margin: 0.2rem 0.2rem 0 0.2rem;
    border-radius: 0.2rem ;
    padding:0.1rem;
    word-break: break-word;
  }
}
</style>
