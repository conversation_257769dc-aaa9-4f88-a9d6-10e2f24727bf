import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const _decoData = {
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/22678/7/20760/97230/66971bfbF2478927d/f27dee4f54df95b3.png',
  actBgColor: '#cc1729',
  btnColor: '#eb3c3c',
  btnBg: '#ffffff',
  btnBorderColor: '#ffffff',
  cutDownBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/137012/1/42465/6275/65e80562F66c606e9/6ab10639ccdebab2.png',
  cutDownColor: '#e2231a',
  signButtonBg: '//img10.360buyimg.com/imgzone/jfs/t1/222296/26/43853/13052/6673db2cFc960e08f/4128190f1f48b5fd.png',
  signButtonColor: '#cc1729',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/122279/21/37921/64583/65327db4F4a3fc7c5/6110574d2be9fe0e.jpg',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/122279/21/37921/64583/65327db4F4a3fc7c5/6110574d2be9fe0e.jpg',
  ruleBtnBg: 'https: //img10.360buyimg.com/imgzone/jfs/t1/235481/36/20858/1427/66752ca9F28e9b68d/9bbe47e8d232e488.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/122279/21/37921/64583/65327db4F4a3fc7c5/6110574d2be9fe0e.jpg',
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '消费时间排名有礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
