<template>
  <div class="rule-bk">
    <div class="title">
      <div class="leftLineDiv"></div>
      <div>中奖记录</div>
      <div class="rightLineDiv"></div>
      <img data-v-705393a4="" alt="" src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png" class="close" @click="close" />
    </div>
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
           <div class="type">
          <span>{{ prizeType[item.prizeType] }}</span>
          <span>{{ item.userPrizeId ? item.userPrizeId : '' }}</span>
        </div>
        <div class="info">
          <img :src="item.prizeImg" alt="" class="show-img" />
          <div class="detail">
            <div class="name">{{ item.prizeName }}</div>
            <div class="time">获奖时间：{{ dayjs(item.time).format('YYYY-MM-DD HH:mm:ss') }}</div>
          </div>
          <div class="status" v-if="item.prizeType === 3">
            <div class="orange" v-if="!item.deliveryStatus">待发货</div>
            <div class="green" v-else>已发货</div>
            <div class="blue" v-if="!item.deliveryStatus" @click="changAddress(item)">修改地址</div>
          </div>
          <div class="status" v-else-if="item.prizeType === 7">
            <!-- <div class="orange">待发货</div> -->
<!--            <div class="blue" @click="showCardNum(item)">如何兑换</div>-->
          </div>
          <div class="status" v-else-if="item.prizeType === 9 || item.prizeType === 10">
            <!-- <div class="orange">待发货</div> -->
            <div class="blue" @click="exchangePlusOrAiqiyi">立即兑换</div>
          </div>
          <div class="status" v-else>
            <div class="green">已发放</div>
          </div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无获奖记录哦~</div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :userPrizeId="userPrizeId" :addressId="addressId" :echoData="echoData" @close="closeSaveAddress"></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast } from 'vant';
import SaveAddress from './SaveAddress.vue';
import { httpRequest } from '@/utils/service';
import { FormType } from '../ts/type';
import { exchangePlusOrAiqiyi } from '@/utils/platforms/jump';

const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  orderList: {
    orderId: string;
    orderStatus: string;
    orderPrice: string;
  }[];
}

const prizes = reactive([] as Prize[]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/95008/userPrizes');
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

const showSaveAddress = ref(false);
const userPrizeId = ref('');
const addressId = ref('');
const echoData: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

// 修改地址
const changAddress = (item: any) => {
  userPrizeId.value = item.userPrizeId;
  addressId.value = item.addressId;
  Object.keys(echoData).forEach((key) => {
    echoData[key] = item[key];
  });
  showSaveAddress.value = true;

};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};

// 展示礼品卡
const showCardNum = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  const { prizeName, prizeImg } = item;
  emits('showCardNum', { ...prizeContent, prizeName, prizeImg });
};

</script>

<style scoped lang="scss">
.rule-bk {
  background-color: #f2f2f2;
  border-radius: 0.2rem 0.2rem 0 0;
  width: 100vw;

  .title {
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.14rem;
    font-size: 0.34rem;
    color: #fff;

    .leftLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, left top, right top, from(#fff), to(#ff6153));
      background: linear-gradient(to right, #fff, #ff6153);
      border-radius: 4px;
      margin-right: 0.1rem;
    }

    .rightLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, right top, left top, from(#fff), to(#ff8c4a));
      background: linear-gradient(to left, #fff, #ff8c4a);
      border-radius: 4px;
      margin-left: 0.1rem;
    }
  }

  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.22rem;
  }

  .content {
    height: 40vh;
    border: 0.3rem solid transparent;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    .prize {
      background: #ffffff;
      margin-bottom: 0.1rem;
      padding-bottom: 0.24rem;
      border-radius: 0.16rem;

      .type {
        color: #999999;
        font-size: 0.2rem;
        text-align: left;
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        padding-top: 0.16rem;
        padding-bottom: 0.16rem;
        border-bottom: 0.02rem dashed #eee;
      }

      .info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 0.24rem;
        margin-left: 0.22rem;
        margin-right: 0.22rem;

        .show-img {
          width: 0.8rem;
          height: 0.8rem;
          border-radius: 50%;
        }

        .detail {
          flex: 1;
          padding-left: 0.28rem;

          .name {
            font-size: 0.28rem;
            color: #ff3333;
          }

          .time {
            color: #999999;
            font-size: 0.2rem;
            margin-top: 0.2rem;
          }
        }

        .status {
          font-size: 0.24rem;
          text-align: right;

          .green {
            color: #6bce98;
          }

          .orange {
            color: #ff9900;
          }

          .blue {
            color: #0083ff;
          }

          .red {
            color: #ff3333;
          }
        }
      }
      .order-list {
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        margin-top: 0.2rem;
        border-top: 0.02rem dashed #eee;
      }
      .order-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 0.2rem;
        font-size: 0.2rem;
        color: #999999;

        .order-id {
          flex: 1.5;
        }
        .order-status {
          flex: 1;
        }
        .order-price {
          flex: 1.1;
        }
      }
    }

    .no-data {
      text-align: center;
       line-height: 3.5rem;
      font-size: 0.24rem;
      color: #8c8c8c;
    }
  }
}
</style>
