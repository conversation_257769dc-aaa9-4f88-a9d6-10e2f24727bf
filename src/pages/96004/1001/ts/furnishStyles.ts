import { computed, reactive } from 'vue';
import { prizePlateArray } from '@/utils/prizePlateArray';

export const prizeInfo = reactive([
  {
    id: '',
    index: 1,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 2,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 3,
    prizeImg: '',
    prizeName: '谢谢参与',
  }, {
    id: '',
    index: 4,
    prizeImg: '',
    prizeName: '谢谢参与',
  }, {
    id: '',
    index: 5,
    prizeImg: '',
    prizeName: '谢谢参与',
  }, {
    id: '',
    index: 6,
    prizeImg: '',
    prizeName: '谢谢参与',
  }, {
    id: '',
    index: 7,
    prizeImg: '',
    prizeName: '谢谢参与',
  }, {
    id: '',
    index: 8,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
]);

export const furnish = reactive({
  pageBg: '',
  actBg: '', // 主页背景图
  actBgColor: '', // 主页背景色
  shopNameColor: '', // 店铺名称颜色
  btnColor: '', // 按钮字体颜色
  btnBg: '', // 按钮背景颜色
  btnBorderColor: '', // 按钮边框颜色
  wheelTextColor: '',
  wheelType: '',
  wheelBg: '', // 大转盘
  wheelPanel: '',
  wheelBtn: '',
  wheelBtnBg: '', // 大转盘
  drawsNum: '', // 剩余抽奖次数颜色
  drawBtn: '', // 抽奖按钮图
  showSkuBg: '', // 展示sku
  ruleBg: '',
  myPrizeBg: '',
  queryOrderBg: '',
  orderSkuBg: '',
  // 是否能关闭入会弹窗
  canNotCloseJoinPopup: 1,
  realWinnersBg: '',
  showWinnersBg: 1, // 是否展示获奖名单背景
  disableShopName: 0,
  showPersonNum: 0,
  personNum: 1000,
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const headerBtn = computed(() => ({
  color: furnish.btnColor ?? '',
  backgroundColor: furnish.btnBg ?? '',
  borderColor: furnish.btnBorderColor ?? '',
  backgroundImage: furnish.ruleBg ? `url("${furnish.ruleBg}")` : '',
}));

const drawsNum = computed(() => ({
  color: furnish.drawsNum ?? '',
}));

const showSkuBg = computed(() => ({
  backgroundImage: furnish.showSkuBg ? `url(${furnish.showSkuBg})` : '',
}));

const realWinnersBg = computed(() => ({
  backgroundImage: furnish.realWinnersBg ? `url(${furnish.realWinnersBg})` : 'url(https://img10.360buyimg.com/imgzone/jfs/t1/219646/3/4738/272448/61946f6dE6e104c93/a65c01e498d00589.png)',
}));

const wheelTpl = {
  hasImg: {
    fonts: [{ text: '', top: '30%', fontColor: 'rgb(131, 75, 235)', fontSize: '0.2rem' }],
    imgs: [
      {
        src: '',
        width: '35%',
        top: '55%',
        borderRadius: '50%',
      },
    ],
  },
  noImg: { fonts: [{ text: '', top: '30%', fontColor: 'rgb(131, 75, 235)', fontSize: '0.2rem' }] },
};

const params = computed(() => {
  const prizePlate = prizePlateArray.find((item: any) => item.splicingBg === furnish.wheelBg);
  const block = {
    padding: '12px',
    imgs: [
      {
        src: furnish.wheelType !== '2' ? prizePlate?.turnUrl : furnish.wheelPanel,
        height: '100%',
        rotate: true,
      },
    ],
  };
  const button = {
    radius: '45%',
    imgs: [
      {
        src: furnish.wheelType !== '2' ? prizePlate?.turnBtn : furnish.wheelBtn,
        width: '60%',
        top: '-80%',
      },
    ],
  };
  const prizes = new Array(8).fill(null);
  for (let i = 0; i < prizes.length; i++) {
    const prize = prizeInfo[i];
    if (prize.prizeImg) {
      prizes[i] = JSON.parse(JSON.stringify(wheelTpl.hasImg));
      prizes[i].imgs[0].src = prize.prizeImg;
    } else {
      prizes[i] = JSON.parse(JSON.stringify(wheelTpl.noImg));
    }
    prizes[i].fonts[0].text = prize.prizeName;
    prizes[i].fonts[0].fontColor = furnish.wheelTextColor ?? '#834beb';
  }
  return {
    blocks: [block],
    buttons: [button],
    prizes,
    defaultConfig: {
      accelerationTime: 2000,
      decelerationTime: 3000,
      speed: 15,
    },
  };
});

export default {
  pageBg,
  shopNameColor,
  headerBtn,
  drawsNum,
  showSkuBg,
  params,
  realWinnersBg,
};
