<template>
  <div class="bg" :style="furnishStyles.pageBg.value" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <div class="header-kv">
      <img :src="furnish.actBg" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.ruleBtn.value" @click="showRule = true"/>
          <div class="header-btn" :style="furnishStyles.myPrizeBtn.value" @click="showMyPrize = true"/>
          <div class="header-btn" :style="furnishStyles.myOrderBtn.value" @click="showMyPrize = true"/>
        </div>
      </div>
    </div>
    <div class="sku" :style="furnishStyles.step1Bg.value">
      <div class="sku-list">
        <div class="sku-item" v-for="(item, index) in orderSkuListPreview" :key="index">
          <div class="sku-name">{{ item.skuName }}</div>
          <img :src="item.skuMainPicture" alt="" />
          <div class="sku-btns"/>
        </div>
        <div class="more-btn" v-if="orderSkuListPreview.length > 18" @click="toast">点我加载更多</div>
      </div>
    </div>
    <div class="prizeBox" :style="furnishStyles.step2Bg.value">
<!--      <img :src="prizeInfo.find((item) => item?.status === 1)?.prizeImg ? prizeInfo.find((item) => item?.status === 1)?.prizeImg : ''" alt="">-->
    </div>
    <div class="step3" :style="furnishStyles.step3Bg.value"/>
    <div class="step4" :style="furnishStyles.step4Bg.value"/>
  </div>
  <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-model:show="showRule" position="bottom">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 评论限制弹窗 -->
  <VanPopup teleport="body" v-model:show="commentRulesPopup" position="bottom">
    <CommentRules v-if="commentRulesPopup" :commentRestrictions="commentRestrictions" :evaluateSkuList="orderSkuListPreview"  :evaluateSkuListAll="evaluateSkuList" @close="commentRulesPopup = false"></CommentRules>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
    <MyPrize @close="showMyPrize = false"></MyPrize>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="showAward">
    <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress"></AwardPopup>
  </VanPopup>
  <!-- 保存地址弹窗 -->
  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress :addressId="addressId" :activityPrizeId="''" @close="showSaveAddress = false"></SaveAddress>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive, inject } from 'vue';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import { CommentRestrictions, Sku } from '../ts/type';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CommentRules from '../components/CommentRules.vue';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import { showToast } from 'vant';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
console.log(activityData);

const shopName = ref('xxx自营旗舰店');

const isLoadingFinish = ref(false);

const showRule = ref(false);
const ruleTest = ref('');
const showMyPrize = ref(false);

// 评价商品
const orderSkuListPreview = ref<Sku[]>([
  {
    skuId: '1',
    skuName: '小羊舒享2段700g',
    skuMainPicture: '//img10.360buyimg.com/imgzone/jfs/t1/289748/3/13408/82338/6846788dF95a9ec40/32c05f735c527200.png'
  },
  {
    skuId: '2',
    skuName: '小羊舒享2段700g哈哈哈哈哈',
    skuMainPicture: 'https://img10.360buyimg.com/imgzone/jfs/t1/304651/24/5339/1391/6833e53fFff8b79b6/1321166842c8c9fb.png'
  },
  {
    skuId: '1',
    skuName: '小羊舒享2段700g',
    skuMainPicture: '//img10.360buyimg.com/imgzone/jfs/t1/289748/3/13408/82338/6846788dF95a9ec40/32c05f735c527200.png'
  },
  {
    skuId: '2',
    skuName: '小羊舒享2段700g哈哈哈哈哈',
    skuMainPicture: 'https://img10.360buyimg.com/imgzone/jfs/t1/304651/24/5339/1391/6833e53fFff8b79b6/1321166842c8c9fb.png'
  },
  {
    skuId: '2',
    skuName: '小羊舒享2段700g哈哈哈哈哈',
    skuMainPicture: 'https://img10.360buyimg.com/imgzone/jfs/t1/304651/24/5339/1391/6833e53fFff8b79b6/1321166842c8c9fb.png'
  },

]);

// 评价限制
const commentRulesPopup = ref(false);
const commentRestrictions = reactive<CommentRestrictions>({
  evaluateStartTime: '',
  evaluateEndTime: '',
  evaluateGrade: 0,
  evaluateNumberType: 0,
  evaluateNumber: 0,
  evaluateImgType: 0,
  evaluateImg: 0,
  evaluateVideoType: 0,
  evaluateVideo: 0,
  evaluateFilterateType: 0,
  evaluateFilterate: '',
  isEvaluateSku: 0,
});

const evaluateSkuList = ref<Sku[]>([]);

// 中奖相关信息
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};

// 页面截图
const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showAward.value = false;
  showSaveAddress.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    console.log('7777', data);
    if (data.prizeList.length) {
      prizeInfo.value = data.prizeList;
    }
    ruleTest.value = data.rules;
    shopName.value = data.shopName;
    if (data.evaluateSkuList) {
      orderSkuListPreview.value = data.evaluateSkuList;
    }
    Object.keys(commentRestrictions).forEach((item) => {
      commentRestrictions[item] = data[item];
    });
  } else if (type === 'screen') {
    createImg();
  }
};

onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    console.log('6666', activityData);
    prizeInfo.splice(0);
    prizeInfo.value = activityData.prizeList;
    ruleTest.value = activityData.rules;
    shopName.value = activityData.shopName;
    if (activityData.orderSkuListPreview) {
      orderSkuListPreview.value = activityData.evaluateSkuList;
    }
    Object.keys(commentRestrictions).forEach((item) => {
      commentRestrictions[item] = activityData[item];
    });
  }
  if (decoData) {
    console.log(decoData);
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>

<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.3rem;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    padding: 0 0.2rem;
    margin-bottom: 0.1rem;
    background-repeat: no-repeat;
    background-size: 100%;
    width: 1.18rem;
    height: 0.44rem;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}

.sku {
  width: 6.82rem;
  height: 9.49rem;
  margin: 0 auto;
  padding: 1rem 0.3rem 0;
  background-size: 100% 100%;

  .more-btn {
    width: 1.8rem;
    height: 0.5rem;
    font-size: 0.2rem;
    color: #fff;
    background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
    background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
    border-radius: 0.25rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0.24rem auto 0.3rem;
  }
  .title-img {
    width: 2.82rem;
    height: 0.4rem;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .sku-list {
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    margin: 0.2rem auto 0.1rem auto;
    padding:0;
    height: 8.3rem;
    overflow: scroll;
    .sku-item {
      margin: 0.2rem 0 0 0;
      border-radius: 0.2rem;
      width: 3.08rem;
      height: 3.73rem;
      /*background: rgb(255, 255, 255);*/
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/301462/37/13268/10615/68465121F46705a3d/59817af780a287f4.png);
      background-size: 100%;
      background-repeat: no-repeat;
      overflow: hidden;
      .sku-name{
        color: #fff;
        width: 2.2rem;
        height: 0.45rem;
        line-height: 0.45rem;
        /* background-color: antiquewhite; */
        padding: 0 0.1rem 0 0.15rem;
        font-size: 0.22rem;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      img{
        height: 2rem;
        margin: 0.45rem auto 0;
      }
    }
  }
}

.prizeBox{
  width: 6.82rem;
  height: 3.37rem;
  margin: 0 auto;
  //padding: 1rem 0.3rem 0;
  background-size: 100%;
}

.step3{
  width: 6.82rem;
  height: 4.57rem;
  margin: 0 auto;
  background-size: 100%;
}

.step4{
  width: 6.82rem;
  height: 4.45rem;
  margin: 0 auto;
  background-size: 100%;
}

</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
