import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { ref } from 'vue';
import '@/components/Threshold2/CPBStyle.scss';

export interface CardType {
  cardDesc: string;
  cardNumber: string;
  cardPassword: string;
  id: number;
  prizeName: string;
  prizeImg: string;
}
export interface FormType {
  realName: string;
  mobile: string;
  province: string;
  city: string;
  county: string;
  address: string;
}

// 展示门槛显示弹框
export const showLimit = ref(false);

export interface Sku {
  jdPrice: number;
  skuId: number;
  skuName: string;
  skuMainPicture: string;
}

export const exposureSkuList = ref<Sku[]>([]);
export const pageNum = ref(1);
export const total = ref(0);

// 获取Sku列表
export const getSkuList = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/99001/getSkuListPage', {
      seriesType: 1,
      pageNum: pageNum.value,
      pageSize: 10,
    });
    closeToast();
    if (res.code === 200) {
      exposureSkuList.value.push(...res.data.records);
      total.value = res.data.total;
    }
    // exposureSkuList.value = res.data;
  } catch (error: any) {
    closeToast();
    console.error();
  }
};
export const loadMore = async () => {
  pageNum.value++;
  await getSkuList();
};
