<template>
  <div class="rule-bk">
    <div class="title">填写信息</div>
    <div class="content">
      <div class="form">
        <div class="cellItem">
          <span>姓名：</span>
          <input type="text" maxlength="20" v-model="form.realName" placeholder="收货人姓名" />
        </div>
        <div class="cellItem">
          <span>电话：</span>
          <input type="text" maxlength="11" name="mobile" @input="validateInput" v-model="form.mobile" placeholder="收货人手机号" />
        </div>
        <div class="cellItem">
          <span>地区：</span>
          <input type="text" v-model="addressCode" readonly @click="addressSelects = true" placeholder="选择省/市/区" />
        </div>
        <div class="cellItem">
          <span>详细地址：</span>
          <input maxlength="500" type="text" v-model="form.address" placeholder="街道门牌号" />
        </div>
      </div>
      <div class="submit" @click="checkForm"></div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
    <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
  </VanPopup>
</template>

<script lang="ts" setup>
import { showToast, closeToast, showLoadingToast } from 'vant';
import { reactive, ref, computed, PropType, onMounted } from 'vue';
import { areaList } from '@vant/area-data';
import { FormType } from '../ts/type';
import { httpRequest } from '@/utils/service';

const props = defineProps({
  addressId: {
    type: String,
    required: true,
  },
  activityPrizeId: {
    type: String,
    required: true,
  },
  echoData: {
    type: Object as PropType<FormType>,
    default: () => ({
      realName: '',
      mobile: '',
      province: '',
      city: '',
      county: '',
      address: '',
    }),
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const addressSelects = ref(false);

const form: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

const validateInput = (event: any) => {
  const { value } = event.target;
  // 只保留数字
  form[event.target.name] = value.replace(/[^0-9]/g, '');
};

onMounted(() => {
  console.log(props.echoData, 'props.echoData');
  // 回显地址
  Object.keys(form).forEach((key: string) => {
    form[key] = props.echoData[key];
  });
});

const addressCode = computed(() => {
  if (form.province && form.city && form.county) {
    return `${form.province}/${form.city}/${form.county}`;
  }
  return '';
});
// const addressCode = ref('');

const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
};

const submit = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/99008/userAddressInfo', {
      addressId: props.addressId,
      activityPrizeId: props.activityPrizeId,
      realName: form.realName,
      mobile: form.mobile,
      province: form.province,
      city: form.city,
      county: form.county,
      address: form.address,
    });
    closeToast();
    if (res.code === 200) {
      showToast('保存成功');
      emits('close', true);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 检查表单
const checkForm = () => {
  const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!form.realName) {
    showToast('请输入姓名');
  } else if (!form.mobile) {
    showToast('请输入电话');
  } else if (!phone.test(form.mobile)) {
    showToast('请输入正确的电话');
  } else if (!form.province) {
    showToast('请选择省市区');
  } else if (!form.address) {
    showToast('请输入详细地址');
  } else {
    submit();
  }
};
</script>

<style scoped lang="scss">
.rule-bk {
  width: 6.5rem;
  height: 7.2rem;
  background: url('../assets/saveAddressPop.png') no-repeat;
  background-size: 100%;
  padding: 1.3rem 0.4rem 0;
  .title {
    text-align: center;
    font-size: 0.43rem;
    color: #8e5913;
    font-weight: bold;
    padding: 0.25rem 0;
  }

  .close {
    position: absolute;
    bottom: 0;
    left: 2.95rem;
    right: 0.26rem;
    width: 0.77rem;
    height: 0.77rem;
  }

  .content {
    .form {
      width: 5.23rem;
      margin: 0 auto;
      padding: 0 0 0.22rem 0;
      font-size: 0.24rem;
      color: #8e5913;
      .cellItem {
        height: 0.6rem;
        margin: 0 auto 0.2rem;
        display: flex;
        align-items: flex-start;
        background-color: #fff;
        border-radius: 0.3rem;
        padding: 0 0.24rem;
        span {
          display: block;
          width: 1.2rem;
          line-height: 0.6rem;
          white-space: nowrap;
        }
        input {
          flex: 1;
          display: block;
          text-align: center;
          border: none;
          outline: none;
          padding: 0;
          margin: 0;
          height: 0.6rem;
          line-height: 0.6rem;
          background-color: transparent;
          text-align: center;
          padding-right: 0.3rem;
          :hover {
            cursor: pointer;
          }
        }
      }
    }

    .submit {
      margin: 0 auto;
      text-align: center;
      width: 3.3rem;
      height: 0.8rem;
    }
  }
}
</style>
