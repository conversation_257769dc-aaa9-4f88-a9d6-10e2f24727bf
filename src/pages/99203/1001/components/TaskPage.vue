<template>
  <div id='task'>
    <img class='back-btn' @click='backToHome()' src='//img10.360buyimg.com/imgzone/jfs/t1/244116/19/21873/3687/67343d90F19ef4609/093e5be67bcd93c4.png' alt=''>
    <div class='title-container'>
      <img alt='' :src='getTaskTitle()'>
    </div>
    <!--    加购、浏览、购买商品-->
    <div v-if='currentTask.taskType===3||currentTask.taskType===7||currentTask.taskType===8' class='sku-list-container' @scroll='handleScroll'>
      <div v-for='(it,index) in skuDetail' :key='index' class='sku-item'>
        <div class='sku-img-part'>
          <img :alt='it' :src='it.skuMainPicture' @click='gotoSkuPage(it.skuId)'>
        </div>
        <div class='sku-name'>{{ it.skuName }}</div>
        <div class='sku-btn-part' :class='{gray:it.status!==0}' @click='doTask(currentTask,it)'>{{ getTaskBtnStatus(currentTask.taskType, it) }}</div>
      </div>
    </div>
    <!--    浏览会场-->
    <div v-if='currentTask.taskType===4' class='venue-container'>
      <div v-for='(it,index) in taskDetail.taskSkus' :key='index' class='venue-item'>
        <img :alt='it' class='venue-img-part' @click='goHref(it.url)' src='//img10.360buyimg.com/imgzone/jfs/t1/197036/22/45244/5372/67359c49F2addf553/d03ef90625c96f06.png'>
        <div class='other-part'>
          <div>{{ it.urlName }}</div>
          <div :class='{gray:it.status!==0}' @click='doTask(currentTask,it)'>立即浏览 ＞</div>
        </div>
      </div>
    </div>
    <!--    邀请入会、邀请参加活动-->
    <div v-if='currentTask.taskType===15 || currentTask.taskType===25' class='share-container'>
      <div class='friend-wrapper'>
        <template v-if='taskDetail.helpMeRecords.length>0'>
          <div v-for='(it,index) in taskDetail.helpMeRecords' :key='index' class='friend-item'>
            <img :alt='it' :src='it.avatar??"//img10.360buyimg.com/imgzone/jfs/t1/160464/30/47276/23712/66de97c2F3fc49a94/9622487457ec9f29.png"'>
            <div>{{ it.helpName }}</div>
          </div>
        </template>
        <template v-else>
          <div v-for='(it,index) in 4' :key='index' class='friend-item'>
            <img :alt='it' src='https://img10.360buyimg.com/imgzone/jfs/t1/211649/19/44953/2468/6731abebF5add088b/0a7d4c405e80a39d.png'>
            <div>账号名称</div>
          </div>
        </template>
      </div>
      <div class='btn-wrapper' @click='shareAct()'>
        <img alt='' src='https://img10.360buyimg.com/imgzone/jfs/t1/183529/14/51730/2730/6731ac41F98c50aab/b03336debb240a8b.png'>
      </div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { computed, PropType, ref, watch, onMounted } from 'vue';
import _ from 'lodash';
import constant from '@/utils/constant';
import { callShare } from '@/utils/platforms/share';
import type { ITask, ITaskSkus } from '../ts/type';
import { gotoSkuPage } from '@/utils/platforms/jump';
import { httpRequest } from '@/utils/service';

type DefineEmits = {
  (e: 'can-toggle-component', componentName: string): void;
};

const emits = defineEmits(['backToHome', 'doTask']);
const props = defineProps({ currentTask: Object as PropType<ITask> });

const taskDetail = computed(() => props.currentTask?.taskDetail);
const skuDetail = ref([]);

const getTaskTitle = () => {
  switch (props.currentTask?.taskType) {
    case 3:
      // 浏览商品
      return 'https://img10.360buyimg.com/imgzone/jfs/t1/179479/28/51425/2749/67356b04Fd906f82f/590561edc57abaac.png';
    case 7:
      // 加购商品
      return 'https://img10.360buyimg.com/imgzone/jfs/t1/204826/17/48533/2569/67356b04F3af8b1dd/6b2dbd2b4e4c7829.png';
    case 8:
      // 购买商品
      return 'https://img10.360buyimg.com/imgzone/jfs/t1/218496/31/41463/2873/67356b04Fd2c5d9a4/ac788e45e76db040.png';
    case 4:
      // 浏览会场
      return 'https://img10.360buyimg.com/imgzone/jfs/t1/132169/14/48968/3457/67356b04F3f032b41/5d3660e25efddce1.png';
    case 15:
      // 邀请好友入会
      return 'https://img10.360buyimg.com/imgzone/jfs/t1/224408/33/25097/4369/67319d79F04a3e3af/ab551ca073dd7304.png';
    case 25:
      // 邀请好友参与活动
      return 'https://img10.360buyimg.com/imgzone/jfs/t1/173817/31/52551/4677/67356b04F981244af/9a07f88b29d319a9.png';
    default:
      return '';
  }
};

const getTaskBtnStatus = (taskType: number, it: ITaskSkus) => {
  if (it.status === 0) {
    switch (taskType) {
      case 3:
        return '立即浏览 ＞';
      case 7:
        return '立即加购 ＞';
      case 8:
        return '立即购买 ＞';
      default:
        return '';
    }
  } else if (it.status === 1) {
    switch (taskType) {
      case 3:
        return '已浏览';
      case 7:
        return '已加购';
      case 8:
        return '立即购买 ＞';
      default:
        return '';
    }
  }
  return '';
};

const goHref = (url: string) => {
  if (url) {
    window.location.href = url;
  }
};

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  console.log(shareConfig, taskDetail.value?.shareId, 999);
  callShare({
    shareUrl: `${window.location.href}&shareIdn=${taskDetail.value?.shareId}&helpType=${props.currentTask?.taskType}`,
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};

const backToHome = () => {
  emits('backToHome');
};

const doTask = (task: ITask, taskDetail: ITaskSkus) => {
  if (taskDetail.status === 0) {
    emits('doTask', task, taskDetail);
  }
};

const isEndFetchData = ref(false);
const pageNum = ref(1);
const fetchSkuData = async () => {
  pageNum.value += 1;
  const { data } = await httpRequest.post('/99203/getTaskDetail', { taskId: props?.currentTask.id, pageNum: pageNum.value });
  if (data.taskDetail?.taskSkus?.length > 0) {
    skuDetail.value.push(...data.taskDetail?.taskSkus);
  } else {
    isEndFetchData.value = true;
  }
};

const handleScroll = (event: any) => {
  const { scrollTop, scrollHeight, clientHeight } = event.target;
  if (scrollTop + clientHeight >= scrollHeight && !isEndFetchData.value) {
    _.throttle(fetchSkuData(), 500);
  }
};

onMounted(() => {
  const { currentTask } = props;
  if (currentTask?.taskType === 3 || currentTask?.taskType === 7 || currentTask?.taskType === 8) {
    skuDetail.value = currentTask?.taskDetail?.taskSkus;
  }
});
</script>

<style lang='scss' scoped>
.gray {
  color: #8b7c7c;
  filter: grayscale(100%);
}

#task {
  width: 100vw;
  max-width: 100vw;
  line-height: 1;
  background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/159592/22/50329/2843/6731678eF6c1aad95/afb356c1b5c65b12.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-color: #eae4df;
  padding-top: 2.6rem;
  padding-bottom: .4rem;
  box-sizing: border-box;
  overflow-y: hidden;

  .back-btn {
    width: 2.11rem;
    position: absolute;
    left: .5rem;
    top: 1rem;
  }

  .gray-filter {
    filter: grayscale(1);
  }

  .title-container {
    height: .37rem;
    display: flex;
    justify-content: center;

    img {
      min-width: 1.49rem;
      height: 100%;
    }
  }

  //加购、浏览、购买商品
  .sku-list-container {
    width: 6.9rem;
    height: 10rem;
    overflow-y: scroll;
    margin: .45rem auto 0;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;

    .sku-item {
      width: 3.27rem;
      display: flex;
      flex-direction: column;
      align-items: center;

      &:nth-child(even) {
        margin-left: .18rem;
      }

      &:nth-child(n+3) {
        margin-top: .28rem;
      }

      .sku-img-part {
        width: 3.27rem;
        height: 3.06rem;
        display: flex;
        justify-content: center;
        align-items: center;
        background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/159174/31/50942/1919/6731a025Fb82c8e6b/dddcea478012c17a.png");
        background-size: contain;
        background-repeat: no-repeat;

        img {
          width: 2.5rem;
          height: 2.5rem;
        }
      }

      .sku-name {
        width: 2rem;
        font-size: .26rem;
        color: #3c3c3c;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        margin-top: .29rem;
        margin-bottom: .2rem;
        text-align: center;
        white-space: nowrap;
        word-break: break-all;
      }

      .sku-btn-part {
        width: 1.96rem;
        height: .45rem;
        line-height: .48rem;
        background-color: #ca8970;
        border-radius: 1000px;
        text-align: center;
        font-size: .22rem;
        color: #ffffff;
      }
    }
  }

  //会场
  .venue-container {
    width: 6.73rem;
    height: 10rem;
    overflow-y: scroll;
    margin: .3rem auto 0;
    padding: 0 .15rem;

    .venue-item {
      width: 100%;
      height: 2.81rem;
      background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/129588/21/51179/2286/6731a69aF8fbcb05f/bb85f5965a49e5ce.png");
      background-size: contain;
      background-repeat: no-repeat;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding: 0 .4rem;
      box-sizing: border-box;

      &:nth-child(n+2) {
        margin-top: .4rem;
      }

      .venue-img-part {
        width: 2.11rem;
        height: 2.11rem;
        border-radius: .25rem;
      }

      .other-part {
        margin-left: 1.03rem;
        display: flex;
        flex-direction: column;
        align-items: center;

        div {
          &:nth-child(1) {
            font-size: .35rem;
            color: #000000;
            font-weight: bold;
            margin-bottom: .3rem;
            width: 2.5rem;
            text-align: justify; /* 两端对齐 */
            text-align-last: right; /* 最后一行右对齐 */
            hyphens: auto; /* 启用连字符，以改善两端对齐的外观 */
            line-height: .4rem;
          }

          &:nth-child(2) {
            width: 1.96rem;
            height: .45rem;
            background-color: #ca8970;
            border-radius: 1000px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: .22rem;
            color: #ffffff;
          }
        }
      }
    }
  }

  .share-container {
    width: 6.73rem;
    min-height: 3.56rem;
    background-color: #FFFFFF;
    border-radius: .3rem;
    margin: .45rem auto 0;
    padding: .6rem 0;
    box-sizing: border-box;

    .friend-wrapper {
      width: 5.84rem;
      max-height: 7.2rem;
      display: flex;
      justify-content: start;
      flex-wrap: wrap;
      overflow-y: auto;
      margin: 0 auto;

      .friend-item {
        width: 1.45rem;
        height: 100%;
        margin-top: .3rem;
        //overflow-y: hidden;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;

        //&:nth-child(n+2) {
        //  margin-left: .5rem;
        //}

        img {
          width: .87rem;
          height: .87rem;
          border-radius: 50%;
        }

        div {
          font-size: .26rem;
          margin-top: .2rem;
          color: #000000;
        }
      }
    }

    .btn-wrapper {
      width: 2.48rem;
      height: .45rem;
      margin: .7rem auto 0;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
