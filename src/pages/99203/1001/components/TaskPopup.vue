<template>
  <div class='task-popup-container'>
    <div class='remaining-counts-wrapper'>当前可用补签卡张数：{{ repairSign }}张</div>
    <div class='task-list-wrapper'>
      <template v-for='(it,index) in taskList' :key='index'>
        <div class='task-item'>
          <img :alt='it' class='task-icon-part' :src='TASK_ENUM[it.taskType].icon'>
          <div class='task-text-part'>
            <div>{{ TASK_ENUM[it.taskType].taskName }}</div>
            <div>{{ getTaskTip(it) }}</div>
          </div>
          <div class='task-btn-part' @click='searchTaskDetail(it.id)'>{{ TASK_ENUM[it.taskType].btnName }}</div>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang='ts' setup>
import type { ITask } from '../ts/type';

const props = defineProps({
  taskList: Array,
  repairSign: Number,
});

const emits = defineEmits(['close', 'searchTaskDetail']);

const TASK_ENUM = {
  3: { btnName: '去浏览 ＞', icon: '//img10.360buyimg.com/imgzone/jfs/t1/123050/1/52839/6917/67359b2dF587e852d/e5a754a998620df8.png', taskName: '浏览商品' },
  7: { btnName: '去加购 ＞', icon: '//img10.360buyimg.com/imgzone/jfs/t1/240059/40/23532/5919/67359b2dFe500ce01/056465eb1668831d.png', taskName: '加购商品' },
  8: { btnName: '去下单 ＞', icon: '//img10.360buyimg.com/imgzone/jfs/t1/131297/38/51330/3983/67359b2dF18e8057a/be07c17f49952cca.png', taskName: '购买商品' },
  4: { btnName: '去浏览 ＞', icon: '//img10.360buyimg.com/imgzone/jfs/t1/197036/22/45244/5372/67359c49F2addf553/d03ef90625c96f06.png', taskName: '浏览会场' },
  15: { btnName: '去邀请 ＞', icon: '//img10.360buyimg.com/imgzone/jfs/t1/189244/25/52058/6323/67359b2dFf8e7df1b/9eacdaf0573a0ade.png', taskName: '邀请好友参与活动' },
  25: { btnName: '去邀请 ＞', icon: '//img10.360buyimg.com/imgzone/jfs/t1/242804/36/21340/5767/67359b2dF1ce5f899/00d32dc8dd1733e9.png', taskName: '邀请好友入会' },
};

const getTaskTip = (task: ITask) => {
  switch (task.taskType) {
    case 3: {
      return `浏览${task.needTimesPerTask}个商品可获得${task.giveChancePerTask}张补签卡`;
    }
    case 7: {
      return `加购${task.needTimesPerTask}个商品可获得${task.giveChancePerTask}张补签卡`;
    }
    case 8: {
      return `购买${task.needTimesPerTask}个商品可获得${task.giveChancePerTask}张补签卡`;
    }
    case 4: {
      return `浏览指定会场可获得${task.giveChancePerTask}张补签卡`;
    }
    case 15: {
      return `邀请${task.needTimesPerTask}位好友参加可获得${task.giveChancePerTask}张补签卡`;
    }
    case 25: {
      return `邀请${task.needTimesPerTask}位好友入会可获得${task.giveChancePerTask}张补签卡`;
    }
    default:
      return '';
  }
};

// 选择任务
const searchTaskDetail = (taskId: string) => {
  emits('searchTaskDetail', taskId);
};

</script>

<style scoped lang='scss'>
.close-btn {
  width: .36rem;
  height: .35rem;
  margin-right: .1rem;

  img {
    width: 100%;
    height: 100%;
  }
}

.task-popup-container {
  width: 7.5rem;
  height: 10.71rem;
  background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/199770/31/47288/40310/673028c1F8cb9fff7/792367f4113d2ad0.png");
  background-size: contain;
  background-repeat: no-repeat;
  margin-top: .2rem;
  padding-top: 1.05rem;
  box-sizing: border-box;

  .remaining-counts-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    font-size: .22rem;
    color: #000;
  }

  .task-list-wrapper {
    width: 6.84rem;
    height: 9rem;
    margin: .36rem auto 0;
    padding-top: .4rem;
    box-sizing: border-box;

    .task-item {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding-left: .38rem;
      box-sizing: border-box;
      height: 1.4rem;

      .task-icon-part {
        width: .97rem;
        height: .98rem;
      }

      .task-text-part {
        margin-left: .24rem;
        width: 3.1rem;

        div {
          &:nth-child(1) {
            font-size: .3rem;
            color: #000;
          }

          &:nth-child(2) {
            font-size: .2rem;
            color: #3c3c3c;
            margin-top: .14rem;
          }
        }
      }

      .task-btn-part {
        width: 1.51rem;
        height: .45rem;
        border-radius: 1000px;
        background-color: #ca8970;
        font-size: .2rem;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: .2rem;
      }
    }
  }
}
</style>
