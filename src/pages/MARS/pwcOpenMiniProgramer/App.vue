<template>
  <div class="main-view">
    <!-- 隐藏的 SVG 滤镜定义 -->
    <svg style="display: none;">
      <defs>
        <filter id="blackwhite-mask">
          <feColorMatrix type="matrix" values="0.33 0.33 0.33 0 0.2
                                                         0.33 0.33 0.33 0 0.2
                                                         0.33 0.33 0.33 0 0.2
                                                         0 0 0 1 0"/>
          <feComponentTransfer>
            <feFuncR type="table" tableValues="0 1"/>
            <feFuncG type="table" tableValues="0 1"/>
            <feFuncB type="table" tableValues="0 1"/>
          </feComponentTransfer>
        </filter>
      </defs>
    </svg>
    <img class="home-dog" :src="require(`./asset/homeDog.png`)"/>
    <img class="home-right-icon" :src="require(`./asset/免责声明.png`)" style="top: 0.5rem;" @click="isShowDisclaimers = true"/>
    <img class="home-right-icon" :src="require(`./asset/适用宠物.png`)" style="top: 1.8rem;" @click="isShowSuitableForPets = true"/>
    <div v-if="!isRead" class="home-bottom-btn" style="position: relative;">
      <img class="btn" @click="joinNow" src="https://img10.360buyimg.com/imgzone/jfs/t1/277557/10/17923/5790/67f50418F3e7ba323/2085a6d3dd4f8f93.png" alt="">
    </div>
    <div v-else class="home-bottom-btn" style="position: relative;">
      <img class="btn" @click="toMp" src="https://img10.360buyimg.com/imgzone/jfs/t1/277557/10/17923/5790/67f50418F3e7ba323/2085a6d3dd4f8f93.png" alt="">
    </div>
    <div class="home-check-box" @click="isRead = !isRead">
      <img v-if="isRead" class="check-icon" :src="require(`./asset/radioIcon-red.png`)"/>
      <img v-else class="check-icon" :src="require(`./asset/radio-icon.png`)"/>
      <img class="check-text" :src="require(`./asset/read-text.png`)"/>
    </div>
    <div class="home-content-view">
      <img class="bg-img" :src="require(`./asset/home-content.png`)"/>
      <div class="click-div" @click="isShowOralHealthIssues = true"></div>
      <div class="bottom-click-div" @click="showhomeLink()"></div>
    </div>
    <Disclaimers :isShow="isShowDisclaimers" @closePopup="closeDisclaimers" @showprivacy="showprivacy" @showtermsOfUse="showtermsOfUse"/>
    <OralHealthIssues :isShow="isShowOralHealthIssues" @closePopup="isShowOralHealthIssues = false" @showprivacy="showprivacy" @showtermsOfUse="showtermsOfUse"/>
    <SuitableForPets :isShow="isShowSuitableForPets" @closePopup="isShowSuitableForPets = false" @showprivacy="showprivacy" @showtermsOfUse="showtermsOfUse"/>
    <LinkPopup :isShow="isShowLinkPopup" :type="linkpoupType" @closePopup="isShowLinkPopup = false"/>
  </div>
</template>
<script lang="ts" setup>
import { provide, ref, onMounted } from 'vue';
import Disclaimers from './components/Disclaimers.vue';
import OralHealthIssues from './components/OralHealthIssues.vue';
import SuitableForPets from './components/SuitableForPets.vue';
import LinkPopup from './components/LinkPopup.vue';
import { showFailToast } from 'vant';
import { PAGE_CONFIG } from './config';

provide('PAGE_CONFIG', PAGE_CONFIG);
interface PetItem {
  id: number;
  petAvatar: string;
  petNick: string;
  petType: number; // 1'dog' | 2'cat'
  petBreed: string;
  petGender: number; // 0:未知，1：公，2：母
}

const petList = ref(false);
const isRead = ref(false);

const isShowDisclaimers = ref(false);
const isShowOralHealthIssues = ref(false);
const isShowSuitableForPets = ref(false);
const isShowLinkPopup = ref(false);
const linkpoupType = ref('');
const showprivacy = () => {
  linkpoupType.value = 'privacyLink';
  isShowLinkPopup.value = true;
  isShowOralHealthIssues.value = false;
  isShowDisclaimers.value = false;
  isShowSuitableForPets.value = false;
};
const showtermsOfUse = () => {
  linkpoupType.value = 'termsOfUse';
  isShowLinkPopup.value = true;
  isShowOralHealthIssues.value = false;
  isShowDisclaimers.value = false;
  isShowSuitableForPets.value = false;
};
const showhomeLink = () => {
  linkpoupType.value = 'homeLink';
  isShowLinkPopup.value = true;
  isShowOralHealthIssues.value = false;
  isShowDisclaimers.value = false;
  isShowSuitableForPets.value = false;
};
const closeDisclaimers = () => {
  isShowDisclaimers.value = false;
};
const joinNow = () => {
  if (!isRead.value) {
    showFailToast('请阅读、知晓并同意本页面免责声明');
  }
};

const petListArr = ref<PetItem[]>([]);
const selectPet = ref();
const openHomeLink = () => {
  window.open(PAGE_CONFIG.homeLink);
};

window.document.title = '幼犬体重日记';
const url = ref('');
// 定义请求签名的函数
const requestSignature = async () => {
  try {
    const response = await fetch(`${process.env.VUE_APP_API_HOST}/common/wx/signature`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url,
      }),
    });
    return await response.json();
  } catch (error) {
    console.error('请求签名失败:', error);
    throw error;
  }
};

// 重构后的 wxSdkConfig 函数
const wxSdkConfig = () => new Promise((resolve) => {
  try {
    console.log('获取微信签名-开始', window.location.href);

    // 使用 async/await 包装的请求函数
    requestSignature()
      .then((data) => {
        console.log('获取微信签名-完成', data);
        const wxConfig = {
          debug: false,
          appId: data.data.appid,
          timestamp: data.data.timestamp,
          nonceStr: data.data.nonceStr,
          signature: data.data.signature,
          openTagList: ['wx-open-launch-weapp'],
          jsApiList: ['updateAppMessageShareData'],
        };

        console.log('微信config-开始', window.location.href);
        window.wx.config(wxConfig);
        console.log('微信config-完成', wxConfig);

        window.wx.ready(() => {
          console.log('wx sdk ready');
        });

        window.wx.error((res: any) => {
          console.error('wx sdk error', res);
        });

        resolve(true);
      })
      .catch((e) => {
        console.log('获取微信签名-失败', e);
        resolve(true);
      });
  } catch (e) {
    console.log('获取微信签名-失败', e);
    resolve(true);
  }
});

function getParams(key: string) {
  const result = new URLSearchParams(window.location.search);
  return result.get(key);
}
const mpUrl: any = getParams('mpUrl');
// const mpUrl = 'https://lzkjdz-isv.isvjd.com/test/cc/custom/demo/share?authType=3';
console.log(mpUrl, 'mpUrl');

url.value = `/pages/wxauth/wxAuth?authDialogHidden=false&bindPhoneDialogHidden=true&enterWebviewUrl=${encodeURIComponent(mpUrl)}`;

const toMp = () => {
  window.wx.miniProgram.navigateTo({
    url: url.value,
  });
};

const init = () => {
  wxSdkConfig();
};

// const PAGE_CONFIG = `PAGE_CONFIG_`

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@import './page.scss';
</style>
<style lang="scss">
.btn {
  position: absolute;
  width: 6.23rem;
  height: 1.13rem;
  left: 50%;
  transform: translateX(-50%);
  top: 0rem;
  display: block;
  overflow: visible;
  z-index: 10;
}
/* 隐藏Webkit内核浏览器的滚动条 */
::-webkit-scrollbar {
  display: none;
}

// 禁止页面回弹行为
html,
body {
  overscroll-behavior-y: none;
  overscroll-behavior-x: none;
}

.van-popup {
  width: 7.5rem;
  background: none;
  overflow-y: unset;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.van-popup--center {
  max-width: 100%;
}
.van-picker {
  width: 100%;
}
.van-toast {
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  word-break: break-word;
  width: fit-content;
  border-radius: 0.1rem;
  padding: 0.1rem;
}
.add-btn {
  width: 1.38rem;
  height: 1.36rem;
  position: fixed;
  right: 0;
  bottom: 1.5rem;
}
.bottom-tab-view {
  width: 7.5rem;
  height: 1.32rem;
  background: #fff;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
  position: fixed;
  left: 0;
  bottom: 0;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/253761/40/8156/6692/67775ce8Ff34cdcaf/977461fdf93fd06f.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  display: flex;
}
</style>
