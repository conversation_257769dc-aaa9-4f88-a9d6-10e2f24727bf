<template>
  <div class="main-view" :style="{paddingTop: '0.43rem'}">
    <div>
      <img class="photo-desc" :src="require(`../../asset/AIdesc.png`)"/>
      <!-- <img class="common-dog" :src="require(`../../asset/photoDog.png`)"/> -->
      <div class="photo-bottom">
        <img class="item-btn" style="width: 3.24rem;" :src="require(`../../asset/select.png`)" @click="handlePhotoSelect"/>
        <img class="item-btn" style="width: 3.21rem;" :src="require(`../../asset/take.png`)" @click="openCamera"/>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted } from 'vue';
import { showLoadingToast, showToast, closeToast } from 'vant';
import { getMobileModel } from '../../config/common';
import { useStore } from 'vuex';
import { RootState } from '../../store/state';
import { useRoute, useRouter } from 'vue-router';
import { lzReportClick } from '@/pages/MARS/lzReport';

import { PAGE_CONFIG } from '../../config/config';

const router = useRouter();
const route = useRoute();

const { petId } = route.query;

const store = useStore<RootState>();

const PetInfo = computed(() => store.state.petInfo);

const needHandleImageSrc = ref('');
const compressorQuality = ref(1);
const handleCompressor = (canvas: any) => {
  canvas.toBlob(
    (blob: Blob) => {
      // 使用FileReader来读取Blob
      const reader = new FileReader();
      reader.onload = () => {
        if (blob.size < 500 * 1024) {
          closeToast();
          router.push('/photoError');
          return;
        }
        if (blob.size >= 3 * 1024 * 1024) {
          compressorQuality.value = Number((compressorQuality.value - 0.1).toFixed(1));
          if (compressorQuality.value > 0) {
            handleCompressor(canvas);
          } else {
            closeToast();
            showToast('图像不符合要求。请上传大小为500 KB到10 MB 之间，格式为PNG、JPG、JPEG或WebP的图像。 请再试一次!');
          }
        } else {
          closeToast();
          sessionStorage.setItem('fileBlobUrl', URL.createObjectURL(blob));
          compressorQuality.value = 1;
          router.push('/preview');
        }
      };
      // 读取Blob为DataURL，同时指定图片格式和质量
      reader.readAsDataURL(blob);
    },
    'image/jpeg',
    compressorQuality.value,
  );
};
const handleAvatarValidate = (file: File) => {
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  const reader = new FileReader();
  reader.onload = (e) => {
    const dataUrl = e.target?.result;
    const image = new Image();
    image.onload = () => {
      // 创建一个 canvas 元素
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      const width0 = image.width;
      const height0 = image.height;

      canvas.width = width0;
      canvas.height = height0;

      // 将图片绘制到 canvas 上
      ctx?.drawImage(image, 0, 0, width0, height0);
      handleCompressor(canvas);
    };
    if (typeof dataUrl === 'string') {
      image.src = dataUrl;
    }
  };
  reader.readAsDataURL(file);
};

/**
 * 选择照片
 */
const handlePhotoSelect = (): void => {
  lzReportClick('clickPhotoSource');
  const app = document.getElementById('container') as HTMLElement;
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/*';
  const system = getMobileModel();
  if (system === 'iPhone') {
    input.accept = 'image/jpeg,image/png,image/jpg';
  } else {
    input.accept = 'image/*';
  }
  input.style.display = 'none';
  app.appendChild(input);
  input.onchange = async (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target && target.files && target.files.length > 0) {
      const file = target.files[0];
      // 进一步处理 file
      if (file) {
        handleAvatarValidate(file);
      }
    }
  };
  input.click();
};

const openCamera = async () => {
  lzReportClick('clickPhotoSource');
  const app = document.getElementById('container') as HTMLElement;
  const input: any = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/*';
  const system = getMobileModel();
  if (system === 'iPhone') {
    input.accept = 'image/jpeg,image/png,image/jpg';
  } else {
    input.accept = 'image/*';
  }
  input.capture = 'camera';
  input.style.display = 'none';
  app.appendChild(input);
  input.onchange = async (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target && target.files && target.files.length > 0) {
      const file = target.files[0];
      // 进一步处理 file
      if (file) {
        handleAvatarValidate(file);
      }
    }
  };
  input.click();
};
const init = () => {
  console.log(PetInfo.value);
};

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
<style lang="scss" scoped>
::-webkit-scrollbar {
  display: none;
}
</style>
