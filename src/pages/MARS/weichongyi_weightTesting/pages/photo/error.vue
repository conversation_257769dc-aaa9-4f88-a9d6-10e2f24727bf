<template>
    <div class="main-view" style="display: flex;align-items: center;">
        <img style="width: 7.5rem;" :src="require(`../../asset/photo/photoErrorDfault.png`)" @click="router.push('/photo')"/>
    </div>
</template>
<script lang="ts" setup>
import { inject, shallowRef, provide, computed, ref, onMounted } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { useStore } from 'vuex';
import { RootState } from '../../store/state';
import { uploadToothImg } from '../../config/api';
import { useRouter } from 'vue-router';

const router = useRouter();
const store = useStore<RootState>();

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('=>(App.vue:23) baseInfo', baseInfo);
const pathParams: any = inject('pathParams');
console.log('=>(App.vue:25) pathParams', pathParams);
const baseUserInfo: any = inject('baseUserInfo');
console.log('=>(App.vue:27) baseUserInfo', baseUserInfo);
const PAGE_CONFIG:any = inject('PAGE_CONFIG');
const PetInfo = computed(() => store.state.petInfo);
const toothImg = computed(() => store.state.toothImg);

const emits = defineEmits(['toggleComponent']);

const init = () => {
  console.log(PAGE_CONFIG);
};

// const PAGE_CONFIG = `PAGE_CONFIG_`

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
