<template>
  <div class="rule-bk">
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div class="info">
          <div class="time">{{ dayjs(item.createTime).format("MM-DD HH:mm:ss") }}</div>
          <div class="name">{{ item.prizeName }}</div>
          <div class="status" v-if="item.prizeType === 3">
            <div class="blue" v-if="!item.realName" @click="changAddress(item)">
              填写地址
            </div>
            <div class="blue" v-else>已填写地址</div>
          </div>
          <div class="status" v-else-if="item.prizeType === 1">
            <div class="blue" @click="gotoSkuPage(item.prizeSkuId)">去使用</div>
          </div>
          <div class="status" v-else>
            <div class="orange" v-if="item.status === 1">已发放</div>
            <div class="green" v-else-if="item.status === 2">发放失败</div>
          </div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无获奖记录哦~</div>
    </div>
    <div class="close" @click="close"></div>
  </div>

  <VanPopup teleport="body" v-model:show="showSaveAddress" position="center">
    <SaveAddress
      v-if="showSaveAddress"
      :addressId="addressId"
      :userPrizeId="userPrizeId"
      :echoData="echoData"
      @close="closeSaveAddress"
    ></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import dayjs from "dayjs";
import { inject, reactive, ref } from "vue";
import { showLoadingToast, closeToast, showToast } from "vant";
import SaveAddress from "./SaveAddress.vue";
import { httpRequest } from "@/utils/service";
import { exchangePlusOrAiqiyi, gotoShopPage, gotoSkuPage } from "@/utils/platforms/jump";
import { BaseInfo } from "@/types/BaseInfo";

const isPreview = inject("isPreview") as boolean;

const baseInfo = inject("baseInfo") as BaseInfo;
const emits = defineEmits(["close"]);
const close = () => {
  emits("close");
};

const prizeType = {
  0: "谢谢参与",
  1: "优惠券",
  2: "京豆",
  3: "实物",
  4: "积分",
  5: "专享价",
  6: "红包",
  7: "礼品卡",
  8: "京东e卡",
  9: "PLUS会员卡",
  10: "爱奇艺会员卡",
  11: "自营令牌促销",
  12: "京元宝",
};

interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  addressId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  status: number;
  realName: string;
  prizeSkuId: string
}

const prizes = reactive([] as Prize[]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/92011/getUserReceiveRecord');
    closeToast();
    prizes.push(...res.data);
  } catch (error) {
    closeToast();
    console.error(error);
  }
};
if (!isPreview) {
  getUserPrizes();
}

const showSaveAddress = ref(false);
const addressId = ref("");
const activityPrizeId = ref("");
const userPrizeId = ref("");
const echoData = reactive({
  realName: "",
  mobile: "",
  province: "",
  city: "",
  county: "",
  address: "",
});

// 修改地址
const changAddress = (item: any) => {
  if (baseInfo.status === 3 && !item.realName) {
    showToast("活动已结束~");
    return;
  }
  addressId.value = item.addressId;
  activityPrizeId.value = item.activityPrizeId;
  userPrizeId.value = item.userPrizeId;
  Object.keys(echoData).forEach((key) => {
    echoData[key] = item[key];
  });
  showSaveAddress.value = true;
};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      prizes.length = 0;
      getUserPrizes();
    }, 1000);
  }
};

// 展示礼品卡
const showCardNum = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  const { prizeName, prizeImg } = item;
  emits("showCardNum", { ...prizeContent, prizeName, showImg: prizeImg });
};

// 领取京元宝
const savePhone = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  emits("savePhone", item.userPrizeId, prizeContent.result.planDesc);
};
</script>

<style scoped lang="scss">
.close {
  width: 0.8rem;
  height: 0.8rem;
  position: absolute;
  bottom: 0.64rem;
  left: 50%;
  transform: translateX(-50%);
  // background-color: red;
}
.rule-bk {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/288134/40/5069/89795/68391335F1e819b97/8ab8841188fb563c.png)
    no-repeat;
  background-size: 100% 100%;
  width: 6.8rem;
  height: 8rem;
  position: relative;
  padding-top: 2.66rem;

  .content {
    width: 5.5rem;
    height: 3.2rem;
    margin: 0.2rem auto 0 auto;
    font-size: 0.16rem;
    white-space: pre-wrap;
    overflow-y: scroll;
    word-wrap: break-word;
    .info {
      display: flex;
      align-items: center;
      margin-bottom: 0.2rem;
      .time,
      .name,
      .status {
        font-size: 0.26rem;
        color: #fff;
        width: 33.33%;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        // padding: 0.15rem 0;
      }
      .name {
        // display: -webkit-box;
        // -webkit-line-clamp: 1; /* 限制显示的行数 */
        // -webkit-box-orient: vertical;
        // overflow: hidden;
        // text-overflow: ellipsis;
      }
      .status {
        div {
          background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/314385/12/5355/783/68391334Fba197b56/5baa02a253e006d8.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 1.45rem;
          height: 0.42rem;
          color: #fff;
          line-height: 0.42rem;
        }
      }
    }
  }
  .no-data {
    font-size: 0.28rem;
    color: #fff;
    text-align: center;
    padding-top: 1.2rem;
  }
}
</style>
