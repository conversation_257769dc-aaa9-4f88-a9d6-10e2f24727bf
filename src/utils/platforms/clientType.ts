// eslint-disable-next-line no-shadow
export const CLIENT_TYPE = {
  WECHAT: 'wx', // 微信,
  QQ: 'qq', // QQ,
  weibo: 'weibo', // 微博,
  JDAPP: 'jd', // 京东app,
  jdlittle: 'jdlittle', // 京东极速版,
  jdh: 'jdh', // 京东健康,
  wxmp: 'mp', // 微信小程序,
  PC: 'pc', // PC端,
  M: 'm', // 移动端,
  OTHER: 'other',
};

export default CLIENT_TYPE;

/**
 * 判断是否是PC端(区分pc端模拟)
 * */
export function isPC() {
  // 检测平台
  const p = navigator.platform;
  const pc1 = p.indexOf('Win') === 0;
  const pc2 = p.indexOf('Mac') === 0;
  return pc1 || pc2;
}

export function isAndroid() {
  return window.jmfe.isAndroid(); // 返回true或false
}

export function isIOS() {
  return window.jmfe.isIOS(); // 返回true或false
}

export function isTablet() {
  return window.jmfe.isTablet(); // 返回true或false
}

export function isMobile() {
  return window.jmfe.isMobile(); // 返回true或false
}

export const getClientType = (): string => {
  // 先试用jdsdk判断一下
  if (window.jmfe.isApp(CLIENT_TYPE.JDAPP) || window.jmfe.isApp(CLIENT_TYPE.jdlittle) || window.jmfe.isApp(CLIENT_TYPE.jdh)) {
    return CLIENT_TYPE.JDAPP;
  }
  if (window.jmfe.isApp(CLIENT_TYPE.WECHAT)) {
    return CLIENT_TYPE.WECHAT;
  }
  if (window.jmfe.isApp(CLIENT_TYPE.QQ)) {
    return CLIENT_TYPE.QQ;
  }
  if (window.jmfe.isApp(CLIENT_TYPE.weibo)) {
    return CLIENT_TYPE.weibo;
  }
  if (window.jmfe.isApp(CLIENT_TYPE.wxmp)) {
    return CLIENT_TYPE.wxmp;
  }
  // jdsdk没判断出来结果，再根据UA判断一下
  const ua = navigator.userAgent;
  if (/jdapp/i.test(ua)) {
    return CLIENT_TYPE.JDAPP;
  }
  if (/MicroMessenger/i.test(ua)) {
    return CLIENT_TYPE.WECHAT;
  }
  if (/MQQBrowser/i.test(ua)) {
    return CLIENT_TYPE.QQ;
  }
  if (isMobile() || isTablet()) {
    return CLIENT_TYPE.M;
  }
  if (isPC()) {
    return CLIENT_TYPE.PC;
  }
  return CLIENT_TYPE.OTHER;
};
