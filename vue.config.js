const WebpackCopyOnPlugin = require('webpack-copy-on-plugin');
const { VantResolver } = require('@vant/auto-import-resolver');
const ComponentsPlugin = require('unplugin-vue-components/webpack');
const path = require('path');
const fs = require('fs');
const tailwindcss = require('tailwindcss');
const autoprefixer = require('autoprefixer');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const archiver = require('archiver');
const fsPromises = fs.promises;

const config = {
  css: {
    loaderOptions: {
      postcss: {
        plugins: [tailwindcss, autoprefixer],
      },
    },
  },
  chainWebpack: (conf) => {
    conf.module
      .rule('ts')
      .use('ts-loader')
      .tap((options) =>
        Object.assign(options, {
          transpileOnly: true,
          compilerOptions: {
            module: 'es2015',
          },
        }),
      );
  },
};

const cmd = process.argv[2];

// 打包时针对某个定制单独打包
if (cmd === 'build') {
  // 获取目标目录
  const targetDir = process.argv[process.argv.length - 1];
  const area = process.argv[process.argv.length - 2];
  const publicPath = `${process.env.VUE_APP_PATH_PREFIX}${targetDir}`;
  const pageRoot = `src/pages/${targetDir}`;
  const outputDir = `dist/${targetDir}`;

  // 没有入口文件 报错
  if (!fs.existsSync(`${pageRoot}/index.ts`)) {
    throw new Error('no entry');
  }

  const plugins = [];

  if (fs.existsSync(`${pageRoot}/public`)) {
    plugins.push(new WebpackCopyOnPlugin('done', {}, {}, [[`${pageRoot}/public`, outputDir]]));
  }

  plugins.push(
    ComponentsPlugin({
      resolvers: [VantResolver()],
    }),
  );

  const normalConfig = {
    publicPath,
    outputDir: `dist/${targetDir}`,
    productionSourceMap: false,
    pages: {
      index: {
        // page 的入口
        entry: pageRoot,
        // 模板来源，如果当前文件夹中有入口文件则使用当前文件下面的，否则使用公用的
        template: fs.existsSync(`${pageRoot}/index.html`) ? `${pageRoot}/index.html` : 'public/index.html',
        // 在 dist/index.html 的输出
        filename: 'index.html',
        // 当使用 title 选项时，
        // template 中的 title 标签需要是 <title><%= htmlWebpackPlugin.options.title %></title>
        // title: 'Index Page',
        // 在这个页面中包含的块，默认情况下会包含
        // 提取出来的通用 chunk 和 vendor chunk。
        chunks: ['chunk-vendors', 'chunk-common', 'index'],
      },
    },
    chainWebpack: (conf) => {
      // 设置图片不转base64格式  图片少 且对图片质量要求高
      const imagesRule = conf.module.rule('images');
      imagesRule.uses.clear(); // 清除原本的images loader配置
      imagesRule
        .test(/\.(jpg|gif|png|svg)$/)
        .exclude.add(path.join(__dirname, '../node_modules')) // 去除node_modules里的图片转base64配置
        .end()
        .use('url-loader')
        .loader('url-loader')
        .options({
          name: 'img/[name].[hash:8].[ext]',
          limit: 1,
        });
    },
    configureWebpack: {
      performance: {
        maxEntrypointSize: 10000000,
        maxAssetSize: 30000000,
      },
      plugins: plugins,
    },
  };

  // 通天塔打包配置
  const babelOutputDir = `dist/${area}/${targetDir}`;

  // 这个函数会检查路径是否存在，如果存在返回路径，否则返回null
  function getValidPath(sourcePath, targetPath) {
    if (fs.existsSync(sourcePath)) {
      return [sourcePath, targetPath];
    }
    return null;
  }

  // 构建路径数组
  const pathsToCopy = [
    getValidPath('babel-build-template/js', `${babelOutputDir}/js`), // 放public/index.html中通用的js
    getValidPath('babel-build-template/css', `${babelOutputDir}/css`), // 放public/index.html中通用的css
    getValidPath(`${pageRoot}/js`, `${babelOutputDir}/js`), // 放自己活动里的js
    getValidPath(`${pageRoot}/css`, `${babelOutputDir}/css`), // 放自己活动里的css
    getValidPath(`${pageRoot}/config.json`, `${babelOutputDir}/config.json`), // 自定义代码配置
  ].filter(Boolean); // 使用 filter(Boolean) 来移除 null 值

  const babelBuildConfig = {
    publicPath: './',
    outputDir: babelOutputDir,
    productionSourceMap: false,
    pages: {
      index: {
        entry: pageRoot,
        chunks: ['chunk-vendors', 'chunk-common', 'index'],
      },
    },
    chainWebpack: (conf) => {
      // 设置图片不转base64格式  图片少 且对图片质量要求高
      const imagesRule = conf.module.rule('images');
      imagesRule.uses.clear(); // 清除原本的images loader配置
      imagesRule
        .test(/\.(jpg|gif|png|svg)$/)
        .exclude.add(path.join(__dirname, '../node_modules')) // 去除node_modules里的图片转base64配置
        .end()
        .use('url-loader')
        .loader('url-loader')
        .options({ name: 'img/[name].[hash:8].[ext]', limit: 1 });

      // 删除 Vue CLI 自动生成的 index.html 插件
      conf.plugins.delete('html-index');
      conf.plugins.delete('preload-index');
      conf.plugins.delete('prefetch-index');

      // 添加自定义的 html-webpack-plugin 插件
      conf.plugin('html-index').use(HtmlWebpackPlugin, [
        {
          template: fs.existsSync(`${pageRoot}/babel-build-template.html`) ? `${pageRoot}/babel-build-template.html` : 'babel-build-template/index.html',
          filename: 'index.html',
          inject: false, // 阻止自动注入资源
          minify: {
            removeComments: true, // 移除 HTML 文件中的注释
          },
        },
      ]);
    },
    configureWebpack: {
      plugins: [
        new WebpackCopyOnPlugin('done', {}, {}, pathsToCopy),
        {
          apply: (compiler) => {
            // 删除文件和目录的通用函数
            const removePaths = async (paths) => {
              for (const filePath of paths) {
                try {
                  if (
                    await fsPromises
                      .access(filePath)
                      .then(() => true)
                      .catch(() => false)
                  ) {
                    const stats = await fsPromises.lstat(filePath);
                    if (stats.isDirectory()) {
                      await fsPromises.rm(filePath, { recursive: true, force: true });
                      console.log(`${filePath} directory has been removed.`);
                    } else {
                      await fsPromises.unlink(filePath);
                      console.log(`${filePath} file has been removed.`);
                    }
                  }
                } catch (error) {
                  console.error(`Error removing ${filePath}:`, error);
                }
              }
            };

            compiler.hooks.afterEmit.tap('DonePlugin', async (compilation) => {
              // 删除多个空行
              const htmlFileName = 'index.html';
              const outputPath = path.join(compilation.outputOptions.path, htmlFileName);
              if (fs.existsSync(outputPath)) {
                const content = fs.readFileSync(outputPath, 'utf-8');
                const result = content.replace(/^\s*[\r\n]/g, '');
                fs.writeFileSync(outputPath, result, 'utf-8');
              }

              // 删除favicon.ico、openJDApp.html
              await removePaths([
                path.join(babelOutputDir, 'favicon.ico'), //
                path.join(babelOutputDir, 'openJDApp.html'),
              ]);

              // 删除statics
              await removePaths([path.join(babelOutputDir, 'statics')]);

              // 自动压缩
              const outputZipPath = path.join(`${compilation.outputOptions.path}.zip`);
              if (fs.existsSync(babelOutputDir)) {
                const archive = archiver('zip');
                const output = fs.createWriteStream(outputZipPath);
                output.on('close', function () {
                  console.log(`通天塔"${targetDir}"打包成功，压缩包体积:${archive.pointer()}bytes`);
                });
                archive.on('error', function (err) {
                  throw err;
                });
                archive.pipe(output);
                archive.directory(babelOutputDir, path.basename(babelOutputDir));
                archive.finalize();
              }
            });
          },
        },
      ],
    },
  };

  if (process.env.VUE_APP_BABEL_MODE) {
    Object.assign(config, babelBuildConfig);
  } else {
    Object.assign(config, normalConfig);
  }
} else {
  // 格式为 src/pages/${brandName}/{path1}/{path2}/.../{path}
  // 截取 src/pages/之后的部分
  console.log('process.argv[process.argv.length - 1]', process.argv[process.argv.length - 1]);
  const targetDir = process.argv[process.argv.length - 1].slice(10).replace(/[\\]+/gi, '/').replace(/^\//, '');
  console.log('🚀 ~ targetDir', targetDir);
  const publicPath = `${process.env.VUE_APP_PATH_PREFIX}${targetDir}`;
  Object.assign(config, {
    publicPath,
    devServer: {
      host: process.env.VUE_APP_DEV_SERVER_HOST,
      port: process.env.VUE_APP_DEV_SERVER_PORT,
      disableHostCheck: true,
      // 直接使用压测环境地址作为测试地址
      proxy: {
        '/api': {
          // 这里最好有一个 /
          // 如果压测环境不能访问，可以使用本地启动的服务
          target: process.env.VUE_APP_API_PROXY_SERVICE,
          ws: true, // 如果要代理 websockets，配置这个参数
          secure: false, // 如果是https接口，需要配置这个参数
          changeOrigin: true, // 是否跨域
          logLevel: 'debug',
          pathRewrite: {
            '/api': '',
          },
        },
      },
    },
    chainWebpack(cfg) {
      cfg.plugin('html').tap((options) => {
        const indexPath = `src/pages/${targetDir}/index.html`;
        options[0].template = fs.existsSync(indexPath) ? indexPath : 'public/index.html';
        options[0].publicPath = process.env.VUE_APP_PATH_PREFIX;
        return options;
      });
    },
    configureWebpack: {
      devtool: 'source-map',
      plugins: [
        ComponentsPlugin({
          resolvers: [VantResolver()],
        }),
      ],
    },
  });
}

module.exports = config;
